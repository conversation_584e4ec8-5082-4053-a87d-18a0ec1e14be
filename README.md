# APISportsGamev2-FECMS

**Frontend Content Management System for APISportsGame**

A comprehensive CMS built with Next.js 15, TypeScript, Ant Design, TanStack Query, and Zustand for managing football data, users, and broadcast links.

## 🚀 Project Overview

APISportsGamev2-FECMS is a modern, responsive frontend CMS designed to manage the APISportsGame platform. It provides comprehensive tools for system administrators to manage users, football data (leagues, teams, fixtures), and broadcast links with advanced features like smart synchronization, role-based access control, and real-time analytics.

### Key Features

- **🔐 System User Management**: Admin/Editor/Moderator role management
- **⚽ Football Data Management**: Leagues, teams, and fixtures with smart sync
- **📺 Broadcast Links Management**: Role-based broadcast link administration
- **📊 Analytics Dashboard**: Real-time statistics and system monitoring
- **🔄 Smart Sync System**: 96% API call reduction with intelligent caching
- **🎨 Modern UI**: Responsive design with Ant Design components
- **🛡️ Security**: JWT authentication with role-based permissions

## 🛠️ Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **UI Library**: Ant Design
- **State Management**: Zustand (global) + TanStack Query (server state)
- **Styling**: Ant Design + Custom CSS
- **API Integration**: Next.js API routes as reverse proxy
- **Development Port**: 4000

## 📁 Project Structure

```
src/
├── app/                    # Next.js 15 App Router
│   ├── api/               # API routes (reverse proxy)
│   ├── dashboard/         # Dashboard pages
│   ├── user-system/       # User management pages
│   ├── football-*/        # Football data pages
│   └── broadcast-links/   # Broadcast management pages
├── components/            # Shared components
├── modules/              # Feature modules
├── lib/                  # Utilities and configurations
├── stores/               # Zustand stores
├── hooks/                # Custom React hooks
├── theme/                # Theme system
└── types/                # TypeScript types
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm/yarn/pnpm
- APISportsGame API running on localhost:3000

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd APISportsGamev2-FECMS
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open the application**
   ```
   http://localhost:4000
   ```

### Environment Setup

The CMS connects to the APISportsGame API via Next.js reverse proxy:
- **CMS**: http://localhost:4000
- **API**: http://localhost:3000
- **API Docs**: http://localhost:3000/api-docs# (admin/admin123456)

## 📋 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

## 🏗️ Development Status

### ✅ Completed Modules

- **Core Infrastructure**: API proxy, state management, theme system
- **Authentication System**: JWT auth with role-based access (dev mode)
- **User System Management**: Complete CRUD for Admin/Editor/Moderator
- **Broadcast Links Management**: Complete CRUD with quality control and language support
- **Layout & Components**: Responsive layout with Ant Design integration

### 🔄 In Progress

- **Module 3.3.1**: Football Leagues Management (Next)

### 📅 Planned Modules

- **Football Leagues Management**: CRUD operations with country filtering
- **Football Teams Management**: Team statistics and league filtering
- **Football Fixtures Management**: Advanced search with sync system
- **Dashboard & Analytics**: System monitoring and quick actions
- **Testing & Optimization**: Unit tests and performance optimization

## 🔐 Authentication & Roles

### Development Mode
Authentication is **disabled** during development for easier testing. All features are accessible without login.

### Production Roles
- **Administrator**: Full system access, user management, system settings
- **Editor**: Content management, football data CRUD, broadcast links
- **Moderator**: Read-only access, limited broadcast management

## 🌐 API Integration

The CMS uses Next.js API routes as a reverse proxy to the main APISportsGame API:

### System Authentication
- `POST /api/system-auth/login` - System user login
- `POST /api/system-auth/create-user` - Create system user
- `GET /api/system-auth/profile` - Get user profile
- `POST /api/system-auth/logout` - Logout

### Football Data
- `GET /api/football/leagues` - Get leagues with filtering
- `GET /api/football/teams` - Get teams with statistics
- `GET /api/football/fixtures` - Get fixtures with advanced filtering
- `GET /api/football/fixtures/sync/status` - Sync status

### Broadcast Links
- `POST /api/broadcast-links` - Create broadcast link
- `GET /api/broadcast-links/fixture/{id}` - Get links by fixture
- `PATCH /api/broadcast-links/{id}` - Update broadcast link

## 🎨 UI/UX Features

- **Responsive Design**: Mobile-first approach with Ant Design
- **Dark/Light Theme**: Automatic theme switching
- **Loading States**: Skeleton loading for better UX
- **Error Handling**: User-friendly error messages
- **Real-time Updates**: Live data synchronization
- **Advanced Search**: Multi-parameter filtering
- **Bulk Operations**: Multi-select actions

## 📊 Performance Features

- **Smart Caching**: TanStack Query with optimized cache strategies
- **Lazy Loading**: Code splitting and component lazy loading
- **Optimistic Updates**: Immediate UI updates with rollback
- **Debounced Search**: Reduced API calls for search operations
- **Pagination**: Server-side pagination for large datasets

## 🛡️ Security Features

- **JWT Authentication**: Access and refresh token system
- **Role-based Access Control**: Granular permissions per role
- **API Rate Limiting**: Protection against abuse
- **Input Validation**: Client and server-side validation
- **Audit Logging**: Track all system changes

## 📖 Documentation

- **Development Rules**: `.augment-rules.md` - Coding standards and guidelines
- **Project Structure**: `project_structure.txt` - Complete project structure
- **Module Progress**: `MODULE_COMPLETION_LOG.md` - Detailed progress tracking
- **Deployment Guide**: `DEPLOYMENT_GUIDE.md` - Production deployment instructions
- **Working Logs**: `LogWorking/` - Detailed completion logs for each module

## 🧪 Testing

```bash
npm run test         # Run unit tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Generate coverage report
```

## 🚀 Deployment

See `DEPLOYMENT_GUIDE.md` for detailed production deployment instructions.

## 📝 Contributing

1. Follow the coding standards in `.augment-rules.md`
2. Create feature branches for new modules
3. Write tests for new functionality
4. Update documentation for changes
5. Create completion logs in `LogWorking/`

## 📞 Support

For questions or issues:
- Check the API documentation at http://localhost:3000/api-docs#
- Review the working logs in `LogWorking/`
- Consult the development rules in `.augment-rules.md`

## 📄 License

[License information to be added]

---

**Last Updated**: 25/05/2024 - After Module 3.2.2 Broadcast Management completion
**Next Milestone**: Module 3.3.1 Football Leagues Management
**Overall Progress**: Core Infrastructure ✅ + User Management ✅ + Broadcast Management ✅ → Football Data Management (In Progress)
