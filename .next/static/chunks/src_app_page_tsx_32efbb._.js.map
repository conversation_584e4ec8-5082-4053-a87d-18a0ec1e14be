{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Card,\n  Button,\n  Space,\n  Typography,\n  Alert,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  LinkOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  ApiOutlined,\n  SettingOutlined,\n  ArrowUpOutlined,\n} from '@ant-design/icons';\n\nconst { Title, Text, Paragraph } = Typography;\n\nexport default function Home() {\n  return (\n    <div>\n      {/* Page Header */}\n      <div className=\"mb-6 flex justify-between items-start\">\n        <div>\n          <Title level={2}>\n            <DashboardOutlined className=\"mr-2\" />\n            Dashboard\n          </Title>\n          <Text type=\"secondary\">\n            Welcome to APISportsGame CMS - Your central hub for managing football data and broadcast links\n          </Text>\n        </div>\n        <Space>\n          <Button icon={<SettingOutlined />}>\n            Settings\n          </Button>\n        </Space>\n      </div>\n      {/* Welcome Alert */}\n      <Alert\n        message=\"Welcome to APISportsGame CMS!\"\n        description=\"This is your central dashboard for managing football leagues, teams, fixtures, broadcast links, and system users. Navigate using the sidebar menu to access different sections.\"\n        type=\"success\"\n        showIcon\n        className=\"mb-6\"\n      />\n\n      {/* Stats Overview */}\n      <Row gutter={16} className=\"mb-6\">\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Total Leagues\"\n              value={25}\n              prefix={<TrophyOutlined />}\n              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>\n              Active football leagues\n            </div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Teams\"\n              value=\"500+\"\n              prefix={<UserOutlined />}\n              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>\n              Registered teams\n            </div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Fixtures\"\n              value={1250}\n              prefix={<CalendarOutlined />}\n              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>\n              Total fixtures\n            </div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card>\n            <Statistic\n              title=\"Broadcast Links\"\n              value={850}\n              prefix={<LinkOutlined />}\n              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>\n              Active links\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Main Content */}\n      <Row gutter={16} className=\"mb-6\">\n        <Col xs={24} md={12}>\n          <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n            <Card title=\"Quick Actions\">\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <Button type=\"primary\" icon={<CalendarOutlined />} block>\n                  Sync Latest Fixtures\n                </Button>\n                <Button icon={<LinkOutlined />} block>\n                  Add Broadcast Link\n                </Button>\n                <Button icon={<UserOutlined />} block>\n                  Create System User\n                </Button>\n                <Button icon={<BarChartOutlined />} block>\n                  View Reports\n                </Button>\n              </Space>\n            </Card>\n\n            <Card title=\"System Overview\">\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div>\n                  <Text strong>API Status:</Text>\n                  <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Online</Text>\n                </div>\n                <div>\n                  <Text strong>Database:</Text>\n                  <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Connected</Text>\n                </div>\n                <div>\n                  <Text strong>External API:</Text>\n                  <Text style={{ color: '#52c41a', marginLeft: '8px' }}>● Syncing</Text>\n                </div>\n                <div>\n                  <Text strong>Last Sync:</Text>\n                  <Text style={{ marginLeft: '8px' }}>2 minutes ago</Text>\n                </div>\n              </Space>\n            </Card>\n          </Space>\n        </Col>\n        <Col xs={24} md={12}>\n          <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n            <Card title=\"Recent Activity\">\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div>\n                  <Text strong>Fixture sync completed</Text>\n                  <br />\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    2 minutes ago\n                  </Text>\n                </div>\n                <div>\n                  <Text strong>New broadcast link added</Text>\n                  <br />\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    5 minutes ago\n                  </Text>\n                </div>\n                <div>\n                  <Text strong>User John Doe logged in</Text>\n                  <br />\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    10 minutes ago\n                  </Text>\n                </div>\n                <div>\n                  <Text strong>System backup completed</Text>\n                  <br />\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    1 hour ago\n                  </Text>\n                </div>\n              </Space>\n            </Card>\n\n            <Card title=\"Quick Links\">\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <Button type=\"link\" href=\"/broadcast-links\" icon={<LinkOutlined />}>\n                  Broadcast Management\n                </Button>\n                <Button type=\"link\" href=\"/users/system\" icon={<UserOutlined />}>\n                  User Management\n                </Button>\n                <Button type=\"link\" href=\"/football/leagues\" icon={<TrophyOutlined />}>\n                  Football Leagues\n                </Button>\n                <Button type=\"link\" href=\"/football/fixtures\" icon={<CalendarOutlined />}>\n                  Fixtures Management\n                </Button>\n              </Space>\n            </Card>\n          </Space>\n        </Col>\n      </Row>\n\n      {/* Getting Started */}\n      <Card title=\"Getting Started\" style={{ marginTop: '24px' }}>\n        <Paragraph>\n          Welcome to the APISportsGame CMS! This dashboard provides you with a comprehensive overview\n          of your football data management system. Here's what you can do:\n        </Paragraph>\n\n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>\n          <div>\n            <Title level={5}>\n              <TrophyOutlined /> Football Data Management\n            </Title>\n            <Text>\n              Manage leagues, teams, and fixtures. Sync data from external APIs and\n              keep your football database up to date.\n            </Text>\n          </div>\n\n          <div>\n            <Title level={5}>\n              <LinkOutlined /> Broadcast Links\n            </Title>\n            <Text>\n              Add and manage broadcast links for fixtures. Control quality settings\n              and ensure reliable streaming sources.\n            </Text>\n          </div>\n\n          <div>\n            <Title level={5}>\n              <UserOutlined /> User System\n            </Title>\n            <Text>\n              Manage system users, roles, and permissions. Control access to different\n              parts of the CMS based on user roles.\n            </Text>\n          </div>\n\n          <div>\n            <Title level={5}>\n              <BarChartOutlined /> System Monitoring\n            </Title>\n            <Text>\n              Monitor API health, view system logs, and track performance metrics\n              to ensure optimal system operation.\n            </Text>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAUA;AAVA;AAAA;AAUA;AAVA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;AAyBA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE9B,SAAS;IACtB,qBACE,6LAAC;;0BAEC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;;kDACZ,6LAAC,+NAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;oCAAS;;;;;;;0CAGxC,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;kCAIzB,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BAAC,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;sCAAK;;;;;;;;;;;;;;;;;0BAMvC,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;0BAIZ,6LAAC,+KAAA,CAAA,MAAG;gBAAC,QAAQ;gBAAI,WAAU;;kCACzB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;;8CACH,6LAAC,2LAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO;oCACP,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;oCACvB,sBAAQ,6LAAC,2NAAA,CAAA,kBAAe;wCAAC,OAAO;4CAAE,OAAO;wCAAU;;;;;;oCACnD,YAAY;wCAAE,OAAO;oCAAU;;;;;;8CAEjC,6LAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAO,UAAU;wCAAQ,OAAO;oCAAO;8CAAG;;;;;;;;;;;;;;;;;kCAKvE,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;;8CACH,6LAAC,2LAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAM;oCACN,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACrB,sBAAQ,6LAAC,2NAAA,CAAA,kBAAe;wCAAC,OAAO;4CAAE,OAAO;wCAAU;;;;;;oCACnD,YAAY;wCAAE,OAAO;oCAAU;;;;;;8CAEjC,6LAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAO,UAAU;wCAAQ,OAAO;oCAAO;8CAAG;;;;;;;;;;;;;;;;;kCAKvE,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;;8CACH,6LAAC,2LAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO;oCACP,sBAAQ,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oCACzB,sBAAQ,6LAAC,2NAAA,CAAA,kBAAe;wCAAC,OAAO;4CAAE,OAAO;wCAAU;;;;;;oCACnD,YAAY;wCAAE,OAAO;oCAAU;;;;;;8CAEjC,6LAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAO,UAAU;wCAAQ,OAAO;oCAAO;8CAAG;;;;;;;;;;;;;;;;;kCAKvE,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;;8CACH,6LAAC,2LAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO;oCACP,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACrB,sBAAQ,6LAAC,2NAAA,CAAA,kBAAe;wCAAC,OAAO;4CAAE,OAAO;wCAAU;;;;;;oCACnD,YAAY;wCAAE,OAAO;oCAAU;;;;;;8CAEjC,6LAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAO,UAAU;wCAAQ,OAAO;oCAAO;8CAAG;;;;;;;;;;;;;;;;;;;;;;;0BAQzE,6LAAC,+KAAA,CAAA,MAAG;gBAAC,QAAQ;gBAAI,WAAU;;kCACzB,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,mMAAA,CAAA,QAAK;4BAAC,WAAU;4BAAW,OAAO;gCAAE,OAAO;4BAAO;4BAAG,MAAK;;8CACzD,6LAAC,iLAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,6LAAC,qMAAA,CAAA,SAAM;gDAAC,MAAK;gDAAU,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gDAAK,KAAK;0DAAC;;;;;;0DAGzD,6LAAC,qMAAA,CAAA,SAAM;gDAAC,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gDAAK,KAAK;0DAAC;;;;;;0DAGtC,6LAAC,qMAAA,CAAA,SAAM;gDAAC,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gDAAK,KAAK;0DAAC;;;;;;0DAGtC,6LAAC,qMAAA,CAAA,SAAM;gDAAC,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gDAAK,KAAK;0DAAC;;;;;;;;;;;;;;;;;8CAM9C,6LAAC,iLAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,6LAAC;;kEACC,6LAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,6LAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;0DAExD,6LAAC;;kEACC,6LAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,6LAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;0DAExD,6LAAC;;kEACC,6LAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,6LAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;0DAExD,6LAAC;;kEACC,6LAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,6LAAC;wDAAK,OAAO;4DAAE,YAAY;wDAAM;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,cAAA,6LAAC,mMAAA,CAAA,QAAK;4BAAC,WAAU;4BAAW,OAAO;gCAAE,OAAO;4BAAO;4BAAG,MAAK;;8CACzD,6LAAC,iLAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,6LAAC;;kEACC,6LAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,6LAAC;;;;;kEACD,6LAAC;wDAAK,MAAK;wDAAY,OAAO;4DAAE,UAAU;wDAAO;kEAAG;;;;;;;;;;;;0DAItD,6LAAC;;kEACC,6LAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,6LAAC;;;;;kEACD,6LAAC;wDAAK,MAAK;wDAAY,OAAO;4DAAE,UAAU;wDAAO;kEAAG;;;;;;;;;;;;0DAItD,6LAAC;;kEACC,6LAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,6LAAC;;;;;kEACD,6LAAC;wDAAK,MAAK;wDAAY,OAAO;4DAAE,UAAU;wDAAO;kEAAG;;;;;;;;;;;;0DAItD,6LAAC;;kEACC,6LAAC;wDAAK,MAAM;kEAAC;;;;;;kEACb,6LAAC;;;;;kEACD,6LAAC;wDAAK,MAAK;wDAAY,OAAO;4DAAE,UAAU;wDAAO;kEAAG;;;;;;;;;;;;;;;;;;;;;;;8CAO1D,6LAAC,iLAAA,CAAA,OAAI;oCAAC,OAAM;8CACV,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAO;;0DACjD,6LAAC,qMAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,MAAK;gDAAmB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;0DAAK;;;;;;0DAGpE,6LAAC,qMAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,MAAK;gDAAgB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;0DAAK;;;;;;0DAGjE,6LAAC,qMAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,MAAK;gDAAoB,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;0DAAK;;;;;;0DAGvE,6LAAC,qMAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,MAAK;gDAAqB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpF,6LAAC,iLAAA,CAAA,OAAI;gBAAC,OAAM;gBAAkB,OAAO;oBAAE,WAAW;gBAAO;;kCACvD,6LAAC;kCAAU;;;;;;kCAKX,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,qBAAqB;4BAAwC,KAAK;wBAAO;;0CACtG,6LAAC;;kDACC,6LAAC;wCAAM,OAAO;;0DACZ,6LAAC,yNAAA,CAAA,iBAAc;;;;;4CAAG;;;;;;;kDAEpB,6LAAC;kDAAK;;;;;;;;;;;;0CAMR,6LAAC;;kDACC,6LAAC;wCAAM,OAAO;;0DACZ,6LAAC,qNAAA,CAAA,eAAY;;;;;4CAAG;;;;;;;kDAElB,6LAAC;kDAAK;;;;;;;;;;;;0CAMR,6LAAC;;kDACC,6LAAC;wCAAM,OAAO;;0DACZ,6LAAC,qNAAA,CAAA,eAAY;;;;;4CAAG;;;;;;;kDAElB,6LAAC;kDAAK;;;;;;;;;;;;0CAMR,6LAAC;;kDACC,6LAAC;wCAAM,OAAO;;0DACZ,6LAAC,6NAAA,CAAA,mBAAgB;;;;;4CAAG;;;;;;;kDAEtB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;KA9OwB"}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}