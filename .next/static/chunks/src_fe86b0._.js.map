{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/types/broadcast.ts"], "sourcesContent": ["/**\n * Broadcast Links Types and Interfaces\n * Comprehensive type definitions for broadcast link management\n */\n\n// Re-export from query types for consistency\nexport type {\n  BroadcastQueries as BroadcastTypes\n} from '@/lib/query-types';\n\n/**\n * Broadcast link quality options\n */\nexport const BROADCAST_QUALITIES = ['HD', 'SD', 'Mobile'] as const;\nexport type BroadcastQuality = typeof BROADCAST_QUALITIES[number];\n\n/**\n * Common broadcast languages\n */\nexport const BROADCAST_LANGUAGES = [\n  'English',\n  'Spanish', \n  'French',\n  'German',\n  'Italian',\n  'Portuguese',\n  'Arabic',\n  'Russian',\n  'Chinese',\n  'Japanese',\n  'Korean',\n  'Other'\n] as const;\nexport type BroadcastLanguage = typeof BROADCAST_LANGUAGES[number];\n\n/**\n * Broadcast link status\n */\nexport const BROADCAST_STATUS = ['active', 'inactive', 'pending', 'blocked'] as const;\nexport type BroadcastStatus = typeof BROADCAST_STATUS[number];\n\n/**\n * Extended broadcast link interface\n */\nexport interface BroadcastLink {\n  id: string;\n  fixtureId: string;\n  fixture?: {\n    id: string;\n    homeTeam: string;\n    awayTeam: string;\n    date: string;\n    league: string;\n    status: string;\n  };\n  url: string;\n  title?: string;\n  description?: string;\n  quality: BroadcastQuality;\n  language: string;\n  isActive: boolean;\n  status: BroadcastStatus;\n  viewCount?: number;\n  rating?: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n  tags?: string[];\n}\n\n/**\n * Broadcast link creation request\n */\nexport interface CreateBroadcastLinkRequest {\n  fixtureId: string;\n  url: string;\n  title?: string;\n  description?: string;\n  quality: BroadcastQuality;\n  language: string;\n  tags?: string[];\n}\n\n/**\n * Broadcast link update request\n */\nexport interface UpdateBroadcastLinkRequest {\n  url?: string;\n  title?: string;\n  description?: string;\n  quality?: BroadcastQuality;\n  language?: string;\n  isActive?: boolean;\n  status?: BroadcastStatus;\n  tags?: string[];\n}\n\n/**\n * Broadcast link query parameters\n */\nexport interface BroadcastLinkQueryParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  fixtureId?: string;\n  quality?: BroadcastQuality;\n  language?: string;\n  isActive?: boolean;\n  status?: BroadcastStatus;\n  createdBy?: string;\n  sortBy?: 'createdAt' | 'updatedAt' | 'viewCount' | 'rating';\n  sortOrder?: 'asc' | 'desc';\n}\n\n/**\n * Broadcast link list response\n */\nexport interface BroadcastLinkListResponse {\n  data: BroadcastLink[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n/**\n * Broadcast link statistics\n */\nexport interface BroadcastLinkStatistics {\n  total: number;\n  active: number;\n  inactive: number;\n  pending: number;\n  blocked: number;\n  byQuality: Record<BroadcastQuality, number>;\n  byLanguage: Record<string, number>;\n  totalViews: number;\n  averageRating: number;\n  recentlyAdded: number; // Last 24 hours\n  topFixtures: Array<{\n    fixtureId: string;\n    fixture: string;\n    linkCount: number;\n  }>;\n}\n\n/**\n * Broadcast link form data\n */\nexport interface BroadcastLinkFormData {\n  fixtureId: string;\n  url: string;\n  title: string;\n  description: string;\n  quality: BroadcastQuality;\n  language: string;\n  tags: string[];\n}\n\n/**\n * Broadcast link validation rules\n */\nexport const BROADCAST_VALIDATION = {\n  url: {\n    required: true,\n    pattern: /^https?:\\/\\/.+/,\n    message: 'Please enter a valid URL starting with http:// or https://'\n  },\n  title: {\n    required: false,\n    minLength: 3,\n    maxLength: 100,\n    message: 'Title must be between 3 and 100 characters'\n  },\n  description: {\n    required: false,\n    maxLength: 500,\n    message: 'Description must not exceed 500 characters'\n  },\n  quality: {\n    required: true,\n    options: BROADCAST_QUALITIES,\n    message: 'Please select a valid quality option'\n  },\n  language: {\n    required: true,\n    message: 'Please select a language'\n  },\n  fixtureId: {\n    required: true,\n    message: 'Please select a fixture'\n  }\n} as const;\n\n/**\n * Broadcast link helper functions\n */\nexport const BroadcastHelpers = {\n  /**\n   * Validate broadcast link URL\n   */\n  isValidUrl: (url: string): boolean => {\n    return BROADCAST_VALIDATION.url.pattern.test(url);\n  },\n\n  /**\n   * Get quality badge color\n   */\n  getQualityColor: (quality: BroadcastQuality): string => {\n    const colors = {\n      HD: 'success',\n      SD: 'warning', \n      Mobile: 'default'\n    };\n    return colors[quality];\n  },\n\n  /**\n   * Get status badge color\n   */\n  getStatusColor: (status: BroadcastStatus): string => {\n    const colors = {\n      active: 'success',\n      inactive: 'default',\n      pending: 'processing',\n      blocked: 'error'\n    };\n    return colors[status];\n  },\n\n  /**\n   * Format view count\n   */\n  formatViewCount: (count: number): string => {\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    }\n    if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  },\n\n  /**\n   * Get language display name\n   */\n  getLanguageDisplayName: (language: string): string => {\n    const languageMap: Record<string, string> = {\n      'en': 'English',\n      'es': 'Spanish',\n      'fr': 'French',\n      'de': 'German',\n      'it': 'Italian',\n      'pt': 'Portuguese',\n      'ar': 'Arabic',\n      'ru': 'Russian',\n      'zh': 'Chinese',\n      'ja': 'Japanese',\n      'ko': 'Korean'\n    };\n    return languageMap[language] || language;\n  },\n\n  /**\n   * Generate broadcast link title from fixture\n   */\n  generateTitle: (fixture: { homeTeam: string; awayTeam: string; league: string }, quality: BroadcastQuality): string => {\n    return `${fixture.homeTeam} vs ${fixture.awayTeam} - ${quality} Stream`;\n  },\n\n  /**\n   * Check if broadcast link is live\n   */\n  isLive: (fixture: { date: string; status: string }): boolean => {\n    return fixture.status === 'LIVE' || fixture.status === 'IN_PLAY';\n  },\n\n  /**\n   * Get fixture display text\n   */\n  getFixtureDisplayText: (fixture: { homeTeam: string; awayTeam: string; date: string }): string => {\n    const date = new Date(fixture.date).toLocaleDateString();\n    return `${fixture.homeTeam} vs ${fixture.awayTeam} (${date})`;\n  }\n};\n\n/**\n * Mock data for development\n */\nexport const MOCK_BROADCAST_LINKS: BroadcastLink[] = [\n  {\n    id: '1',\n    fixtureId: 'fixture-1',\n    fixture: {\n      id: 'fixture-1',\n      homeTeam: 'Manchester United',\n      awayTeam: 'Liverpool',\n      date: '2024-05-26T15:00:00Z',\n      league: 'Premier League',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream1.example.com/match1',\n    title: 'Manchester United vs Liverpool - HD Stream',\n    description: 'High quality stream for Premier League match',\n    quality: 'HD',\n    language: 'English',\n    isActive: true,\n    status: 'active',\n    viewCount: 15420,\n    rating: 4.5,\n    createdBy: 'admin',\n    createdAt: '2024-05-25T10:00:00Z',\n    updatedAt: '2024-05-25T10:00:00Z',\n    tags: ['premier-league', 'hd', 'english']\n  },\n  {\n    id: '2',\n    fixtureId: 'fixture-1',\n    fixture: {\n      id: 'fixture-1',\n      homeTeam: 'Manchester United',\n      awayTeam: 'Liverpool',\n      date: '2024-05-26T15:00:00Z',\n      league: 'Premier League',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream2.example.com/match1-mobile',\n    title: 'Manchester United vs Liverpool - Mobile Stream',\n    description: 'Mobile optimized stream',\n    quality: 'Mobile',\n    language: 'English',\n    isActive: true,\n    status: 'active',\n    viewCount: 8930,\n    rating: 4.2,\n    createdBy: 'editor1',\n    createdAt: '2024-05-25T11:00:00Z',\n    updatedAt: '2024-05-25T11:00:00Z',\n    tags: ['premier-league', 'mobile', 'english']\n  },\n  {\n    id: '3',\n    fixtureId: 'fixture-2',\n    fixture: {\n      id: 'fixture-2',\n      homeTeam: 'Barcelona',\n      awayTeam: 'Real Madrid',\n      date: '2024-05-27T20:00:00Z',\n      league: 'La Liga',\n      status: 'SCHEDULED'\n    },\n    url: 'https://stream3.example.com/clasico',\n    title: 'El Clasico - HD Stream',\n    description: 'Barcelona vs Real Madrid in HD',\n    quality: 'HD',\n    language: 'Spanish',\n    isActive: false,\n    status: 'pending',\n    viewCount: 0,\n    rating: 0,\n    createdBy: 'editor2',\n    createdAt: '2024-05-25T12:00:00Z',\n    updatedAt: '2024-05-25T12:00:00Z',\n    tags: ['la-liga', 'clasico', 'spanish']\n  }\n];\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,6CAA6C;;;;;;;;;AAQtC,MAAM,sBAAsB;IAAC;IAAM;IAAM;CAAS;AAMlD,MAAM,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAMM,MAAM,mBAAmB;IAAC;IAAU;IAAY;IAAW;CAAU;AA4HrE,MAAM,uBAAuB;IAClC,KAAK;QACH,UAAU;QACV,SAAS;QACT,SAAS;IACX;IACA,OAAO;QACL,UAAU;QACV,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA,aAAa;QACX,UAAU;QACV,WAAW;QACX,SAAS;IACX;IACA,SAAS;QACP,UAAU;QACV,SAAS;QACT,SAAS;IACX;IACA,UAAU;QACR,UAAU;QACV,SAAS;IACX;IACA,WAAW;QACT,UAAU;QACV,SAAS;IACX;AACF;AAKO,MAAM,mBAAmB;IAC9B;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,qBAAqB,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;IAC/C;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,MAAM,SAAS;YACb,IAAI;YACJ,IAAI;YACJ,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,QAAQ;IACxB;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,MAAM,SAAS;YACb,QAAQ;YACR,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA,OAAO,MAAM,CAAC,OAAO;IACvB;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C;QACA,IAAI,SAAS,MAAM;YACjB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA;;GAEC,GACD,wBAAwB,CAAC;QACvB,MAAM,cAAsC;YAC1C,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,OAAO,WAAW,CAAC,SAAS,IAAI;IAClC;IAEA;;GAEC,GACD,eAAe,CAAC,SAAiE;QAC/E,OAAO,GAAG,QAAQ,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,GAAG,EAAE,QAAQ,OAAO,CAAC;IACzE;IAEA;;GAEC,GACD,QAAQ,CAAC;QACP,OAAO,QAAQ,MAAM,KAAK,UAAU,QAAQ,MAAM,KAAK;IACzD;IAEA;;GAEC,GACD,uBAAuB,CAAC;QACtB,MAAM,OAAO,IAAI,KAAK,QAAQ,IAAI,EAAE,kBAAkB;QACtD,OAAO,GAAG,QAAQ,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC/D;AACF;AAKO,MAAM,uBAAwC;IACnD;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAkB;YAAM;SAAU;IAC3C;IACA;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAkB;YAAU;SAAU;IAC/C;IACA;QACE,IAAI;QACJ,WAAW;QACX,SAAS;YACP,IAAI;YACJ,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,KAAK;QACL,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAW;YAAW;SAAU;IACzC;CACD"}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/broadcast-links/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Card,\n  <PERSON>readcrumb,\n  Typography,\n  Button,\n  Space,\n  Tag,\n  Descriptions,\n  Alert,\n  Spin,\n  Row,\n  Col,\n  Statistic,\n  Tooltip,\n  Popconfirm,\n  message\n} from 'antd';\nimport {\n  HomeOutlined,\n  PlayCircleOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  <PERSON>Outlined,\n  EyeOutlined,\n  GlobalOutlined,\n  CalendarOutlined,\n  UserOutlined,\n  StarOutlined,\n  TagsOutlined,\n  ShareAltOutlined\n} from '@ant-design/icons';\nimport { useRouter, useParams } from 'next/navigation';\nimport Link from 'next/link';\nimport { MOCK_BROADCAST_LINKS, BroadcastHelpers } from '@/types/broadcast';\nimport { useBroadcastLink, useDeleteBroadcastLink } from '@/hooks/api/broadcast-hooks';\n\nconst { Title, Text, Paragraph } = Typography;\n\nexport default function BroadcastLinkDetailPage() {\n  const router = useRouter();\n  const params = useParams();\n  const linkId = params.id as string;\n\n  // For development, use mock data\n  const broadcastLinkQuery = {\n    data: MOCK_BROADCAST_LINKS.find(link => link.id === linkId),\n    isLoading: false,\n    error: null\n  };\n\n  const deleteLink = useDeleteBroadcastLink();\n\n  const handleDelete = async () => {\n    try {\n      await deleteLink.mutateAsync(linkId);\n      message.success('Broadcast link deleted successfully');\n      router.push('/broadcast-links');\n    } catch (error) {\n      message.error('Failed to delete broadcast link');\n    }\n  };\n\n  const handleEdit = () => {\n    router.push(`/broadcast-links/${linkId}/edit`);\n  };\n\n  // Loading state\n  if (broadcastLinkQuery.isLoading) {\n    return (\n      <div className=\"p-6\">\n        <div className=\"flex justify-center items-center h-64\">\n          <Spin size=\"large\" />\n        </div>\n      </div>\n    );\n  }\n\n  // Error state\n  if (broadcastLinkQuery.error) {\n    return (\n      <div className=\"p-6\">\n        <Alert\n          message=\"Error Loading Broadcast Link\"\n          description=\"Failed to load broadcast link data. Please try again.\"\n          type=\"error\"\n          showIcon\n        />\n      </div>\n    );\n  }\n\n  // Not found state\n  if (!broadcastLinkQuery.data) {\n    return (\n      <div className=\"p-6\">\n        <Alert\n          message=\"Broadcast Link Not Found\"\n          description=\"The requested broadcast link could not be found.\"\n          type=\"warning\"\n          showIcon\n          action={\n            <Link href=\"/broadcast-links\">\n              <Button type=\"primary\">Back to Broadcast Links</Button>\n            </Link>\n          }\n        />\n      </div>\n    );\n  }\n\n  const broadcastLink = broadcastLinkQuery.data;\n  const fixture = broadcastLink.fixture;\n\n  return (\n    <div>\n      {/* Breadcrumb Navigation */}\n      <Breadcrumb\n        className=\"mb-4\"\n        items={[\n          {\n            href: '/',\n            title: <HomeOutlined />\n          },\n          {\n            href: '/broadcast-links',\n            title: (\n              <>\n                <PlayCircleOutlined />\n                <span className=\"ml-1\">Broadcast Links</span>\n              </>\n            )\n          },\n          {\n            title: broadcastLink.title || 'Broadcast Link Details'\n          }\n        ]}\n      />\n\n      {/* Page Header */}\n      <div className=\"mb-6 flex justify-between items-start\">\n        <div>\n          <Title level={2}>\n            <PlayCircleOutlined className=\"mr-2\" />\n            {broadcastLink.title || 'Broadcast Link Details'}\n          </Title>\n          <Text type=\"secondary\">\n            Detailed information about this broadcast link\n          </Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<EditOutlined />}\n            onClick={handleEdit}\n          >\n            Edit\n          </Button>\n          <Popconfirm\n            title=\"Delete Broadcast Link\"\n            description=\"Are you sure you want to delete this broadcast link? This action cannot be undone.\"\n            onConfirm={handleDelete}\n            okText=\"Yes, Delete\"\n            cancelText=\"Cancel\"\n            okButtonProps={{ danger: true }}\n          >\n            <Button\n              danger\n              icon={<DeleteOutlined />}\n              loading={deleteLink.isPending}\n            >\n              Delete\n            </Button>\n          </Popconfirm>\n        </Space>\n      </div>\n\n      {/* Status Alert */}\n      {!broadcastLink.isActive && (\n        <Alert\n          message=\"Inactive Broadcast Link\"\n          description=\"This broadcast link is currently inactive and not visible to users.\"\n          type=\"warning\"\n          showIcon\n          className=\"mb-6\"\n        />\n      )}\n\n      {/* Live Status */}\n      {fixture && BroadcastHelpers.isLive(fixture) && (\n        <Alert\n          message=\"Live Match\"\n          description=\"This broadcast link is for a currently live match.\"\n          type=\"success\"\n          showIcon\n          className=\"mb-6\"\n        />\n      )}\n\n      <Row gutter={24}>\n        {/* Main Information */}\n        <Col xs={24} lg={16}>\n          {/* Fixture Information */}\n          {fixture && (\n            <Card title=\"Fixture Information\" className=\"mb-6\">\n              <Descriptions column={1}>\n                <Descriptions.Item label=\"Match\">\n                  <Text strong className=\"text-lg\">\n                    {fixture.homeTeam} vs {fixture.awayTeam}\n                  </Text>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"League\">\n                  {fixture.league}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"Date & Time\">\n                  <CalendarOutlined className=\"mr-2\" />\n                  {new Date(fixture.date).toLocaleString()}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"Status\">\n                  <Tag color={BroadcastHelpers.isLive(fixture) ? 'red' : 'blue'}>\n                    {fixture.status}\n                  </Tag>\n                </Descriptions.Item>\n              </Descriptions>\n            </Card>\n          )}\n\n          {/* Stream Information */}\n          <Card title=\"Stream Information\" className=\"mb-6\">\n            <Descriptions column={1}>\n              <Descriptions.Item label=\"Stream URL\">\n                <div className=\"flex items-center gap-2\">\n                  <Text code className=\"break-all\">{broadcastLink.url}</Text>\n                  <Tooltip title=\"Open stream in new tab\">\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      icon={<ShareAltOutlined />}\n                      onClick={() => window.open(broadcastLink.url, '_blank')}\n                    />\n                  </Tooltip>\n                </div>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"Quality\">\n                <Tag color={BroadcastHelpers.getQualityColor(broadcastLink.quality)}>\n                  {broadcastLink.quality}\n                </Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"Language\">\n                <Tag icon={<GlobalOutlined />}>\n                  {broadcastLink.language}\n                </Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"Status\">\n                <Tag color={BroadcastHelpers.getStatusColor(broadcastLink.status)}>\n                  {broadcastLink.status.toUpperCase()}\n                </Tag>\n                <Tag color={broadcastLink.isActive ? 'success' : 'default'} className=\"ml-2\">\n                  {broadcastLink.isActive ? 'Active' : 'Inactive'}\n                </Tag>\n              </Descriptions.Item>\n              {broadcastLink.description && (\n                <Descriptions.Item label=\"Description\">\n                  <Paragraph>{broadcastLink.description}</Paragraph>\n                </Descriptions.Item>\n              )}\n              {broadcastLink.tags && broadcastLink.tags.length > 0 && (\n                <Descriptions.Item label=\"Tags\">\n                  <Space wrap>\n                    {broadcastLink.tags.map(tag => (\n                      <Tag key={tag} icon={<TagsOutlined />}>\n                        {tag}\n                      </Tag>\n                    ))}\n                  </Space>\n                </Descriptions.Item>\n              )}\n            </Descriptions>\n          </Card>\n\n          {/* Metadata */}\n          <Card title=\"Metadata\">\n            <Descriptions column={2}>\n              <Descriptions.Item label=\"Created By\">\n                <UserOutlined className=\"mr-2\" />\n                {broadcastLink.createdBy}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"Created At\">\n                {new Date(broadcastLink.createdAt).toLocaleString()}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"Last Updated\">\n                {new Date(broadcastLink.updatedAt).toLocaleString()}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"Link ID\">\n                <Text code>{broadcastLink.id}</Text>\n              </Descriptions.Item>\n            </Descriptions>\n          </Card>\n        </Col>\n\n        {/* Statistics Sidebar */}\n        <Col xs={24} lg={8}>\n          {/* Performance Stats */}\n          <Card title=\"Performance Statistics\" className=\"mb-6\">\n            <Space direction=\"vertical\" className=\"w-full\">\n              <Statistic\n                title=\"Total Views\"\n                value={broadcastLink.viewCount || 0}\n                prefix={<EyeOutlined />}\n                formatter={(value) => BroadcastHelpers.formatViewCount(Number(value))}\n              />\n              {broadcastLink.rating && (\n                <Statistic\n                  title=\"User Rating\"\n                  value={broadcastLink.rating}\n                  precision={1}\n                  prefix={<StarOutlined />}\n                  suffix=\"/ 5.0\"\n                  valueStyle={{ color: '#faad14' }}\n                />\n              )}\n            </Space>\n          </Card>\n\n          {/* Quick Actions */}\n          <Card title=\"Quick Actions\">\n            <Space direction=\"vertical\" className=\"w-full\">\n              <Button\n                block\n                icon={<LinkOutlined />}\n                onClick={() => window.open(broadcastLink.url, '_blank')}\n              >\n                Test Stream Link\n              </Button>\n              <Button\n                block\n                icon={<EditOutlined />}\n                onClick={handleEdit}\n              >\n                Edit Details\n              </Button>\n              <Button\n                block\n                icon={<PlayCircleOutlined />}\n                onClick={() => router.push('/broadcast-links')}\n              >\n                View All Links\n              </Button>\n            </Space>\n          </Card>\n\n          {/* Related Information */}\n          {fixture && (\n            <Card title=\"Related Information\">\n              <Space direction=\"vertical\" className=\"w-full\">\n                <Text>\n                  <strong>Fixture ID:</strong> {fixture.id}\n                </Text>\n                <Text>\n                  <strong>League:</strong> {fixture.league}\n                </Text>\n                <Button\n                  block\n                  type=\"dashed\"\n                  onClick={() => message.info('Fixture details coming soon')}\n                >\n                  View Fixture Details\n                </Button>\n              </Space>\n            </Card>\n          )}\n        </Col>\n      </Row>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAkCA;AACA;AACA;AACA;AAlCA;AAAA;AAAA;AAiBA;AAAA;AAjBA;AAAA;AAiBA;AAjBA;AAiBA;AAjBA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAjBA;AAAA;AAiBA;AAAA;AAAA;AAAA;AAjBA;AAiBA;AAAA;AAAA;AAjBA;;;AAHA;;;;;;;AAuCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE9B,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IAExB,iCAAiC;IACjC,MAAM,qBAAqB;QACzB,MAAM,4HAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACpD,WAAW;QACX,OAAO;IACT;IAEA,MAAM,aAAa,CAAA,GAAA,4IAAA,CAAA,yBAAsB,AAAD;IAExC,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,WAAW,CAAC;YAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,OAAO,KAAK,CAAC;IAC/C;IAEA,gBAAgB;IAChB,IAAI,mBAAmB,SAAS,EAAE;QAChC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAK;;;;;;;;;;;;;;;;IAInB;IAEA,cAAc;IACd,IAAI,mBAAmB,KAAK,EAAE;QAC5B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;;;;;;;;;;;IAIhB;IAEA,kBAAkB;IAClB,IAAI,CAAC,mBAAmB,IAAI,EAAE;QAC5B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,sBACE,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;wBAAC,MAAK;kCAAU;;;;;;;;;;;;;;;;;;;;;IAMnC;IAEA,MAAM,gBAAgB,mBAAmB,IAAI;IAC7C,MAAM,UAAU,cAAc,OAAO;IAErC,qBACE,6LAAC;;0BAEC,6LAAC,6LAAA,CAAA,aAAU;gBACT,WAAU;gBACV,OAAO;oBACL;wBACE,MAAM;wBACN,qBAAO,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACtB;oBACA;wBACE,MAAM;wBACN,qBACE;;8CACE,6LAAC,iOAAA,CAAA,qBAAkB;;;;;8CACnB,6LAAC;oCAAK,WAAU;8CAAO;;;;;;;;oBAG7B;oBACA;wBACE,OAAO,cAAc,KAAK,IAAI;oBAChC;iBACD;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;;kDACZ,6LAAC,iOAAA,CAAA,qBAAkB;wCAAC,WAAU;;;;;;oCAC7B,cAAc,KAAK,IAAI;;;;;;;0CAE1B,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;kCAIzB,6LAAC,mMAAA,CAAA,QAAK;;0CACJ,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gCACnB,SAAS;0CACV;;;;;;0CAGD,6LAAC,6LAAA,CAAA,aAAU;gCACT,OAAM;gCACN,aAAY;gCACZ,WAAW;gCACX,QAAO;gCACP,YAAW;gCACX,eAAe;oCAAE,QAAQ;gCAAK;0CAE9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAM;oCACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oCACrB,SAAS,WAAW,SAAS;8CAC9B;;;;;;;;;;;;;;;;;;;;;;;YAQN,CAAC,cAAc,QAAQ,kBACtB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;YAKb,WAAW,4HAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,0BAClC,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;0BAId,6LAAC,+KAAA,CAAA,MAAG;gBAAC,QAAQ;;kCAEX,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;;4BAEd,yBACC,6LAAC,iLAAA,CAAA,OAAI;gCAAC,OAAM;gCAAsB,WAAU;0CAC1C,cAAA,6LAAC,iNAAA,CAAA,eAAY;oCAAC,QAAQ;;sDACpB,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACvB,cAAA,6LAAC;gDAAK,MAAM;gDAAC,WAAU;;oDACpB,QAAQ,QAAQ;oDAAC;oDAAK,QAAQ,QAAQ;;;;;;;;;;;;sDAG3C,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACtB,QAAQ,MAAM;;;;;;sDAEjB,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;;8DACvB,6LAAC,6NAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;gDAC3B,IAAI,KAAK,QAAQ,IAAI,EAAE,cAAc;;;;;;;sDAExC,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACvB,cAAA,6LAAC,+KAAA,CAAA,MAAG;gDAAC,OAAO,4HAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,WAAW,QAAQ;0DACpD,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC,iLAAA,CAAA,OAAI;gCAAC,OAAM;gCAAqB,WAAU;0CACzC,cAAA,6LAAC,iNAAA,CAAA,eAAY;oCAAC,QAAQ;;sDACpB,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACvB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,IAAI;wDAAC,WAAU;kEAAa,cAAc,GAAG;;;;;;kEACnD,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;4DACvB,SAAS,IAAM,OAAO,IAAI,CAAC,cAAc,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;sDAKtD,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACvB,cAAA,6LAAC,+KAAA,CAAA,MAAG;gDAAC,OAAO,4HAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,cAAc,OAAO;0DAC/D,cAAc,OAAO;;;;;;;;;;;sDAG1B,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACvB,cAAA,6LAAC,+KAAA,CAAA,MAAG;gDAAC,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;0DACvB,cAAc,QAAQ;;;;;;;;;;;sDAG3B,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;;8DACvB,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAO,4HAAA,CAAA,mBAAgB,CAAC,cAAc,CAAC,cAAc,MAAM;8DAC7D,cAAc,MAAM,CAAC,WAAW;;;;;;8DAEnC,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAO,cAAc,QAAQ,GAAG,YAAY;oDAAW,WAAU;8DACnE,cAAc,QAAQ,GAAG,WAAW;;;;;;;;;;;;wCAGxC,cAAc,WAAW,kBACxB,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACvB,cAAA,6LAAC;0DAAW,cAAc,WAAW;;;;;;;;;;;wCAGxC,cAAc,IAAI,IAAI,cAAc,IAAI,CAAC,MAAM,GAAG,mBACjD,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACvB,cAAA,6LAAC,mMAAA,CAAA,QAAK;gDAAC,IAAI;0DACR,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,oBACtB,6LAAC,+KAAA,CAAA,MAAG;wDAAW,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;kEAC/B;uDADO;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWtB,6LAAC,iLAAA,CAAA,OAAI;gCAAC,OAAM;0CACV,cAAA,6LAAC,iNAAA,CAAA,eAAY;oCAAC,QAAQ;;sDACpB,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;;8DACvB,6LAAC,qNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDACvB,cAAc,SAAS;;;;;;;sDAE1B,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACtB,IAAI,KAAK,cAAc,SAAS,EAAE,cAAc;;;;;;sDAEnD,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACtB,IAAI,KAAK,cAAc,SAAS,EAAE,cAAc;;;;;;sDAEnD,6LAAC,iNAAA,CAAA,eAAY,CAAC,IAAI;4CAAC,OAAM;sDACvB,cAAA,6LAAC;gDAAK,IAAI;0DAAE,cAAc,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOpC,6LAAC,+KAAA,CAAA,MAAG;wBAAC,IAAI;wBAAI,IAAI;;0CAEf,6LAAC,iLAAA,CAAA,OAAI;gCAAC,OAAM;gCAAyB,WAAU;0CAC7C,cAAA,6LAAC,mMAAA,CAAA,QAAK;oCAAC,WAAU;oCAAW,WAAU;;sDACpC,6LAAC,2LAAA,CAAA,YAAS;4CACR,OAAM;4CACN,OAAO,cAAc,SAAS,IAAI;4CAClC,sBAAQ,6LAAC,mNAAA,CAAA,cAAW;;;;;4CACpB,WAAW,CAAC,QAAU,4HAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,OAAO;;;;;;wCAE/D,cAAc,MAAM,kBACnB,6LAAC,2LAAA,CAAA,YAAS;4CACR,OAAM;4CACN,OAAO,cAAc,MAAM;4CAC3B,WAAW;4CACX,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;4CACrB,QAAO;4CACP,YAAY;gDAAE,OAAO;4CAAU;;;;;;;;;;;;;;;;;0CAOvC,6LAAC,iLAAA,CAAA,OAAI;gCAAC,OAAM;0CACV,cAAA,6LAAC,mMAAA,CAAA,QAAK;oCAAC,WAAU;oCAAW,WAAU;;sDACpC,6LAAC,qMAAA,CAAA,SAAM;4CACL,KAAK;4CACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4CACnB,SAAS,IAAM,OAAO,IAAI,CAAC,cAAc,GAAG,EAAE;sDAC/C;;;;;;sDAGD,6LAAC,qMAAA,CAAA,SAAM;4CACL,KAAK;4CACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4CACnB,SAAS;sDACV;;;;;;sDAGD,6LAAC,qMAAA,CAAA,SAAM;4CACL,KAAK;4CACL,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;4CACzB,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;;;;;;;;;;;;4BAOJ,yBACC,6LAAC,iLAAA,CAAA,OAAI;gCAAC,OAAM;0CACV,cAAA,6LAAC,mMAAA,CAAA,QAAK;oCAAC,WAAU;oCAAW,WAAU;;sDACpC,6LAAC;;8DACC,6LAAC;8DAAO;;;;;;gDAAoB;gDAAE,QAAQ,EAAE;;;;;;;sDAE1C,6LAAC;;8DACC,6LAAC;8DAAO;;;;;;gDAAgB;gDAAE,QAAQ,MAAM;;;;;;;sDAE1C,6LAAC,qMAAA,CAAA,SAAM;4CACL,KAAK;4CACL,MAAK;4CACL,SAAS,IAAM,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;sDAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAhVwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QAUL,4IAAA,CAAA,yBAAsB;;;KAZnB"}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}