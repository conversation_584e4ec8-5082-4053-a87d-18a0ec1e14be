'use client';

import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Space,
  Alert,
  Tag,
  Divider,
  Row,
  Col,
  Typography,
  Switch,
  Rate,
  message
} from 'antd';
import {
  LinkOutlined,
  PlayCircleOutlined,
  GlobalOutlined,
  TagsOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import {
  BroadcastLink,
  CreateBroadcastLinkRequest,
  UpdateBroadcastLinkRequest,
  BroadcastLinkFormData,
  BROADCAST_QUALITIES,
  BROADCAST_LANGUAGES,
  BROADCAST_VALIDATION,
  BroadcastHelpers
} from '@/types/broadcast';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface BroadcastFormProps {
  initialData?: Partial<BroadcastLink>;
  onSubmit: (data: CreateBroadcastLinkRequest | UpdateBroadcastLinkRequest) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  mode?: 'create' | 'edit';
  fixtures?: Array<{
    id: string;
    homeTeam: string;
    awayTeam: string;
    date: string;
    league: string;
    status: string;
  }>;
}

export function BroadcastForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode = 'create',
  fixtures = []
}: BroadcastFormProps) {
  const [form] = Form.useForm();
  const [urlValid, setUrlValid] = useState<boolean | null>(null);
  const [selectedFixture, setSelectedFixture] = useState<string | undefined>(initialData?.fixtureId);

  // Initialize form with data
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({
        fixtureId: initialData.fixtureId,
        url: initialData.url,
        title: initialData.title || '',
        description: initialData.description || '',
        quality: initialData.quality || 'HD',
        language: initialData.language || 'English',
        isActive: initialData.isActive ?? true,
        tags: initialData.tags || []
      });
      setSelectedFixture(initialData.fixtureId);
    }
  }, [initialData, form]);

  // Validate URL
  const validateUrl = (url: string) => {
    const isValid = BroadcastHelpers.isValidUrl(url);
    setUrlValid(isValid);
    return isValid;
  };

  // Handle form submission
  const handleSubmit = async (values: BroadcastLinkFormData) => {
    try {
      const submitData = mode === 'create' 
        ? {
            fixtureId: values.fixtureId,
            url: values.url,
            title: values.title,
            description: values.description,
            quality: values.quality,
            language: values.language,
            tags: values.tags
          } as CreateBroadcastLinkRequest
        : {
            url: values.url,
            title: values.title,
            description: values.description,
            quality: values.quality,
            language: values.language,
            isActive: values.isActive,
            tags: values.tags
          } as UpdateBroadcastLinkRequest;

      await onSubmit(submitData);
      message.success(`Broadcast link ${mode === 'create' ? 'created' : 'updated'} successfully`);
      
      if (mode === 'create') {
        form.resetFields();
        setUrlValid(null);
        setSelectedFixture(undefined);
      }
    } catch (error) {
      message.error(`Failed to ${mode} broadcast link`);
    }
  };

  // Auto-generate title when fixture or quality changes
  const handleFixtureOrQualityChange = () => {
    const fixtureId = form.getFieldValue('fixtureId');
    const quality = form.getFieldValue('quality');
    const currentTitle = form.getFieldValue('title');
    
    if (fixtureId && quality && !currentTitle) {
      const fixture = fixtures.find(f => f.id === fixtureId);
      if (fixture) {
        const generatedTitle = BroadcastHelpers.generateTitle(fixture, quality);
        form.setFieldValue('title', generatedTitle);
      }
    }
  };

  const selectedFixtureData = fixtures.find(f => f.id === selectedFixture);

  return (
    <Card>
      <Title level={4}>
        <PlayCircleOutlined className="mr-2" />
        {mode === 'create' ? 'Create Broadcast Link' : 'Edit Broadcast Link'}
      </Title>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          quality: 'HD',
          language: 'English',
          isActive: true,
          tags: []
        }}
      >
        {/* Fixture Selection */}
        {mode === 'create' && (
          <Form.Item
            name="fixtureId"
            label="Fixture"
            rules={[{ required: true, message: BROADCAST_VALIDATION.fixtureId.message }]}
          >
            <Select
              placeholder="Select a fixture"
              showSearch
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
              }
              onChange={(value) => {
                setSelectedFixture(value);
                handleFixtureOrQualityChange();
              }}
            >
              {fixtures.map(fixture => (
                <Option key={fixture.id} value={fixture.id}>
                  <div>
                    <Text strong>{BroadcastHelpers.getFixtureDisplayText(fixture)}</Text>
                    <br />
                    <Text type="secondary" className="text-sm">
                      {fixture.league} • {fixture.status}
                      {BroadcastHelpers.isLive(fixture) && (
                        <Tag color="red" className="ml-2">LIVE</Tag>
                      )}
                    </Text>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {/* Selected Fixture Info */}
        {selectedFixtureData && (
          <Alert
            message={
              <div>
                <Text strong>{BroadcastHelpers.getFixtureDisplayText(selectedFixtureData)}</Text>
                <br />
                <Text type="secondary">{selectedFixtureData.league}</Text>
                {BroadcastHelpers.isLive(selectedFixtureData) && (
                  <Tag color="red" className="ml-2">LIVE</Tag>
                )}
              </div>
            }
            type="info"
            showIcon
            className="mb-4"
          />
        )}

        <Row gutter={16}>
          <Col xs={24} md={12}>
            {/* URL */}
            <Form.Item
              name="url"
              label="Stream URL"
              rules={[
                { required: true, message: BROADCAST_VALIDATION.url.message },
                {
                  validator: (_, value) => {
                    if (!value || validateUrl(value)) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error(BROADCAST_VALIDATION.url.message));
                  }
                }
              ]}
            >
              <Input
                prefix={<LinkOutlined />}
                placeholder="https://stream.example.com/match"
                onChange={(e) => validateUrl(e.target.value)}
                status={urlValid === false ? 'error' : urlValid === true ? 'success' : undefined}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            {/* Quality */}
            <Form.Item
              name="quality"
              label="Quality"
              rules={[{ required: true, message: BROADCAST_VALIDATION.quality.message }]}
            >
              <Select onChange={handleFixtureOrQualityChange}>
                {BROADCAST_QUALITIES.map(quality => (
                  <Option key={quality} value={quality}>
                    <Tag color={BroadcastHelpers.getQualityColor(quality)}>
                      {quality}
                    </Tag>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} md={12}>
            {/* Title */}
            <Form.Item
              name="title"
              label="Title (Optional)"
              rules={[
                { min: BROADCAST_VALIDATION.title.minLength, message: BROADCAST_VALIDATION.title.message },
                { max: BROADCAST_VALIDATION.title.maxLength, message: BROADCAST_VALIDATION.title.message }
              ]}
            >
              <Input
                placeholder="Auto-generated from fixture and quality"
                suffix={
                  <Button
                    type="text"
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={handleFixtureOrQualityChange}
                    title="Auto-generate title"
                  />
                }
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            {/* Language */}
            <Form.Item
              name="language"
              label="Language"
              rules={[{ required: true, message: BROADCAST_VALIDATION.language.message }]}
            >
              <Select
                showSearch
                placeholder="Select language"
                filterOption={(input, option) =>
                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                }
              >
                {BROADCAST_LANGUAGES.map(language => (
                  <Option key={language} value={language}>
                    <GlobalOutlined className="mr-2" />
                    {language}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* Description */}
        <Form.Item
          name="description"
          label="Description (Optional)"
          rules={[
            { max: BROADCAST_VALIDATION.description.maxLength, message: BROADCAST_VALIDATION.description.message }
          ]}
        >
          <TextArea
            rows={3}
            placeholder="Additional information about the stream..."
            showCount
            maxLength={BROADCAST_VALIDATION.description.maxLength}
          />
        </Form.Item>

        {/* Tags */}
        <Form.Item
          name="tags"
          label="Tags (Optional)"
        >
          <Select
            mode="tags"
            placeholder="Add tags (press Enter to add)"
            tokenSeparators={[',']}
            suffixIcon={<TagsOutlined />}
          >
            <Option value="hd">HD</Option>
            <Option value="mobile">Mobile</Option>
            <Option value="live">Live</Option>
            <Option value="free">Free</Option>
            <Option value="premium">Premium</Option>
          </Select>
        </Form.Item>

        {/* Active Status (Edit mode only) */}
        {mode === 'edit' && (
          <Form.Item
            name="isActive"
            label="Status"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="Active"
              unCheckedChildren="Inactive"
            />
          </Form.Item>
        )}

        <Divider />

        {/* Form Actions */}
        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              {mode === 'create' ? 'Create Broadcast Link' : 'Update Broadcast Link'}
            </Button>
            {onCancel && (
              <Button onClick={onCancel}>
                Cancel
              </Button>
            )}
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}

export default BroadcastForm;
