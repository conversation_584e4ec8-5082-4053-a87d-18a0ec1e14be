/**
 * UI Components Index
 * Export all common UI components
 */

// Button components
export * from './button';

// Input components
export * from './input';

// Card components
export * from './card';

// Re-export commonly used Ant Design components for convenience
export {
  Typography,
  Space,
  Divider,
  Tag,
  Badge,
  Avatar,
  Tooltip,
  Popover,
  Dropdown,
  Menu,
  Breadcrumb,
  Steps,
  Progress,
  Spin,
  Alert,
  Message,
  Modal,
  Drawer,
  Popconfirm,
  Input,
  Select,
  Form,
  Button as AntButton,
  Table as AntTable,
  Row,
  Col,
  DatePicker,
} from 'antd';

// Export notification separately (it's not a component but an API)
export { notification } from 'antd';

/**
 * UI components metadata
 */
export const UI_COMPONENTS_VERSION = '1.0.0';
export const UI_COMPONENTS_NAME = 'APISportsGame UI Components';

/**
 * Setup function for UI components
 */
export function setupUIComponents() {
  console.log(`${UI_COMPONENTS_NAME} v${UI_COMPONENTS_VERSION} initialized`);
}
