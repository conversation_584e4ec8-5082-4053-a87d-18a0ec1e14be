/**
 * App Sidebar Component
 * Navigation sidebar for the APISportsGame CMS
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Layout, Menu, Divider } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  TrophyOutlined,
  CalendarOutlined,
  LinkOutlined,
  SettingOutlined,
  BarChartOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  ApiOutlined,
  HeartOutlined,
  PlusOutlined,
  PlayCircleOutlined,
  ExperimentOutlined,
} from '@ant-design/icons';
import { useThemeStyles } from '@/theme';
import { useRouter, usePathname } from 'next/navigation';

const { Sider } = Layout;

/**
 * App sidebar props
 */
export interface AppSidebarProps {
  collapsed: boolean;
  isMobile: boolean;
  onCollapse: (collapsed: boolean) => void;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Menu item interface
 */
interface MenuItem {
  key: string;
  icon: React.ReactNode;
  label: string;
  path?: string;
  children?: MenuItem[];
  disabled?: boolean;
}

/**
 * App Sidebar component
 */
export function AppSidebar({
  collapsed,
  isMobile,
  onCollapse,
  className,
  style,
}: AppSidebarProps) {
  const themeStyles = useThemeStyles();
  const router = useRouter();
  const pathname = usePathname();

  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [openKeys, setOpenKeys] = useState<string[]>([]);

  // Menu items configuration
  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
      path: '/',
    },
    {
      key: 'divider-1',
      icon: null,
      label: '',
    },
    {
      key: 'user-management',
      icon: <UserOutlined />,
      label: 'User System',
      children: [
        {
          key: 'system-users',
          icon: <TeamOutlined />,
          label: 'System Users',
          path: '/users/system',
        },
        {
          key: 'user-roles',
          icon: <SettingOutlined />,
          label: 'Roles & Permissions',
          path: '/users/roles',
        },
      ],
    },
    {
      key: 'football-management',
      icon: <TrophyOutlined />,
      label: 'Football Data',
      children: [
        {
          key: 'leagues',
          icon: <TrophyOutlined />,
          label: 'Leagues',
          path: '/football/leagues',
        },
        {
          key: 'teams',
          icon: <TeamOutlined />,
          label: 'Teams',
          path: '/football/teams',
        },
        {
          key: 'fixtures',
          icon: <CalendarOutlined />,
          label: 'Fixtures',
          path: '/football/fixtures',
        },
        {
          key: 'sync-status',
          icon: <DatabaseOutlined />,
          label: 'Sync Status',
          path: '/football/sync',
        },
      ],
    },
    {
      key: 'broadcast-management',
      icon: <PlayCircleOutlined />,
      label: 'Broadcast Links',
      children: [
        {
          key: 'broadcast-links',
          icon: <LinkOutlined />,
          label: 'Manage Links',
          path: '/broadcast-links',
        },
        {
          key: 'broadcast-create',
          icon: <PlusOutlined />,
          label: 'Create Link',
          path: '/broadcast-links/create',
        },
        {
          key: 'broadcast-demo',
          icon: <ExperimentOutlined />,
          label: 'Demo & Testing',
          path: '/broadcast-demo',
        },
      ],
    },
    {
      key: 'divider-2',
      icon: null,
      label: '',
    },
    {
      key: 'system',
      icon: <SettingOutlined />,
      label: 'System',
      children: [
        {
          key: 'api-health',
          icon: <HeartOutlined />,
          label: 'API Health',
          path: '/system/health',
        },
        {
          key: 'api-docs',
          icon: <ApiOutlined />,
          label: 'API Documentation',
          path: '/system/api-docs',
        },
        {
          key: 'logs',
          icon: <FileTextOutlined />,
          label: 'System Logs',
          path: '/system/logs',
        },
        {
          key: 'settings',
          icon: <SettingOutlined />,
          label: 'Settings',
          path: '/system/settings',
        },
      ],
    },
    {
      key: 'divider-3',
      icon: null,
      label: '',
    },
    {
      key: 'demos',
      icon: <BarChartOutlined />,
      label: 'Demos',
      children: [
        {
          key: 'components-demo',
          icon: <BarChartOutlined />,
          label: 'Components',
          path: '/components-demo',
        },
        {
          key: 'theme-demo',
          icon: <BarChartOutlined />,
          label: 'Theme System',
          path: '/theme-demo',
        },
        {
          key: 'api-hooks-demo',
          icon: <ApiOutlined />,
          label: 'API Hooks',
          path: '/api-hooks-demo',
        },
      ],
    },
  ];

  // Update selected keys based on current pathname
  useEffect(() => {
    const findSelectedKey = (items: MenuItem[], path: string): string | null => {
      for (const item of items) {
        if (item.path === path) {
          return item.key;
        }
        if (item.children) {
          const childKey = findSelectedKey(item.children, path);
          if (childKey) {
            return childKey;
          }
        }
      }
      return null;
    };

    const selectedKey = findSelectedKey(menuItems, pathname);
    if (selectedKey) {
      setSelectedKeys([selectedKey]);

      // Auto-expand parent menu
      const findParentKey = (items: MenuItem[], targetKey: string): string | null => {
        for (const item of items) {
          if (item.children) {
            const hasChild = item.children.some(child => child.key === targetKey);
            if (hasChild) {
              return item.key;
            }
          }
        }
        return null;
      };

      const parentKey = findParentKey(menuItems, selectedKey);
      if (parentKey && !collapsed) {
        setOpenKeys([parentKey]);
      }
    }
  }, [pathname, collapsed]);

  // Handle menu click
  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) {
          return item;
        }
        if (item.children) {
          const childItem = findMenuItem(item.children, targetKey);
          if (childItem) {
            return childItem;
          }
        }
      }
      return null;
    };

    const menuItem = findMenuItem(menuItems, key);
    if (menuItem?.path) {
      router.push(menuItem.path);

      // Close sidebar on mobile after navigation
      if (isMobile) {
        onCollapse(true);
      }
    }
  };

  // Handle submenu open/close
  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  // Convert menu items to Ant Design menu format
  const convertToAntMenuItems = (items: MenuItem[]): any[] => {
    return items.map(item => {
      // Handle dividers
      if (item.key.startsWith('divider')) {
        return {
          type: 'divider',
          key: item.key,
        };
      }

      // Handle regular items
      if (item.children) {
        return {
          key: item.key,
          icon: item.icon,
          label: item.label,
          children: convertToAntMenuItems(item.children),
          disabled: item.disabled,
        };
      }

      return {
        key: item.key,
        icon: item.icon,
        label: item.label,
        disabled: item.disabled,
      };
    });
  };

  const siderStyle: React.CSSProperties = {
    position: 'fixed',
    left: 0,
    top: '64px', // Header height
    bottom: 0,
    zIndex: isMobile ? 1000 : 100,
    backgroundColor: themeStyles.getBackgroundColor('container'),
    borderRight: `1px solid ${themeStyles.getBorderColor('primary')}`,
    overflow: 'auto',
    ...style,
  };

  return (
    <Sider
      className={className}
      style={siderStyle}
      collapsed={collapsed}
      collapsible={false}
      width={250}
      collapsedWidth={80}
      theme="light"
    >
      {/* Sidebar content */}
      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Main navigation menu */}
        <Menu
          mode="inline"
          selectedKeys={selectedKeys}
          openKeys={collapsed ? [] : openKeys}
          onOpenChange={handleOpenChange}
          onClick={handleMenuClick}
          items={convertToAntMenuItems(menuItems)}
          style={{
            flex: 1,
            border: 'none',
            backgroundColor: 'transparent',
          }}
        />

        {/* Sidebar footer */}
        {!collapsed && (
          <div
            style={{
              padding: '16px',
              borderTop: `1px solid ${themeStyles.getBorderColor('primary')}`,
              textAlign: 'center',
            }}
          >
            <div
              style={{
                fontSize: '12px',
                color: themeStyles.getTextColor('tertiary'),
                marginBottom: '4px',
              }}
            >
              APISportsGame CMS
            </div>
            <div
              style={{
                fontSize: '10px',
                color: themeStyles.getTextColor('tertiary'),
              }}
            >
              v1.0.0
            </div>
          </div>
        )}
      </div>
    </Sider>
  );
}

/**
 * Sidebar menu item component for custom rendering
 */
export interface SidebarMenuItemProps {
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  collapsed?: boolean;
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

export function SidebarMenuItem({
  icon,
  label,
  active = false,
  collapsed = false,
  onClick,
  className,
  style,
}: SidebarMenuItemProps) {
  const themeStyles = useThemeStyles();

  const itemStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: collapsed ? '0' : '12px',
    padding: '12px 16px',
    cursor: 'pointer',
    borderRadius: '6px',
    margin: '2px 8px',
    transition: 'all 0.2s ease',
    backgroundColor: active ? themeStyles.getColor('primary') + '10' : 'transparent',
    color: active ? themeStyles.getColor('primary') : themeStyles.getTextColor('primary'),
    ...style,
  };

  return (
    <div
      className={className}
      style={itemStyle}
      onClick={onClick}
    >
      <div style={{ fontSize: '16px', minWidth: '16px' }}>
        {icon}
      </div>
      {!collapsed && (
        <div style={{ fontSize: '14px', fontWeight: active ? 'bold' : 'normal' }}>
          {label}
        </div>
      )}
    </div>
  );
}
