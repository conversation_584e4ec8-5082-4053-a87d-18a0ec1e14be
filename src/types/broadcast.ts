/**
 * Broadcast Links Types and Interfaces
 * Comprehensive type definitions for broadcast link management
 */

// Re-export from query types for consistency
export type {
  BroadcastQueries as BroadcastTypes
} from '@/lib/query-types';

/**
 * Broadcast link quality options
 */
export const BROADCAST_QUALITIES = ['HD', 'SD', 'Mobile'] as const;
export type BroadcastQuality = typeof BROADCAST_QUALITIES[number];

/**
 * Common broadcast languages
 */
export const BROADCAST_LANGUAGES = [
  'English',
  'Spanish', 
  'French',
  'German',
  'Italian',
  'Portuguese',
  'Arabic',
  'Russian',
  'Chinese',
  'Japanese',
  'Korean',
  'Other'
] as const;
export type BroadcastLanguage = typeof BROADCAST_LANGUAGES[number];

/**
 * Broadcast link status
 */
export const BROADCAST_STATUS = ['active', 'inactive', 'pending', 'blocked'] as const;
export type BroadcastStatus = typeof BROADCAST_STATUS[number];

/**
 * Extended broadcast link interface
 */
export interface BroadcastLink {
  id: string;
  fixtureId: string;
  fixture?: {
    id: string;
    homeTeam: string;
    awayTeam: string;
    date: string;
    league: string;
    status: string;
  };
  url: string;
  title?: string;
  description?: string;
  quality: BroadcastQuality;
  language: string;
  isActive: boolean;
  status: BroadcastStatus;
  viewCount?: number;
  rating?: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tags?: string[];
}

/**
 * Broadcast link creation request
 */
export interface CreateBroadcastLinkRequest {
  fixtureId: string;
  url: string;
  title?: string;
  description?: string;
  quality: BroadcastQuality;
  language: string;
  tags?: string[];
}

/**
 * Broadcast link update request
 */
export interface UpdateBroadcastLinkRequest {
  url?: string;
  title?: string;
  description?: string;
  quality?: BroadcastQuality;
  language?: string;
  isActive?: boolean;
  status?: BroadcastStatus;
  tags?: string[];
}

/**
 * Broadcast link query parameters
 */
export interface BroadcastLinkQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  fixtureId?: string;
  quality?: BroadcastQuality;
  language?: string;
  isActive?: boolean;
  status?: BroadcastStatus;
  createdBy?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'viewCount' | 'rating';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Broadcast link list response
 */
export interface BroadcastLinkListResponse {
  data: BroadcastLink[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Broadcast link statistics
 */
export interface BroadcastLinkStatistics {
  total: number;
  active: number;
  inactive: number;
  pending: number;
  blocked: number;
  byQuality: Record<BroadcastQuality, number>;
  byLanguage: Record<string, number>;
  totalViews: number;
  averageRating: number;
  recentlyAdded: number; // Last 24 hours
  topFixtures: Array<{
    fixtureId: string;
    fixture: string;
    linkCount: number;
  }>;
}

/**
 * Broadcast link form data
 */
export interface BroadcastLinkFormData {
  fixtureId: string;
  url: string;
  title: string;
  description: string;
  quality: BroadcastQuality;
  language: string;
  tags: string[];
}

/**
 * Broadcast link validation rules
 */
export const BROADCAST_VALIDATION = {
  url: {
    required: true,
    pattern: /^https?:\/\/.+/,
    message: 'Please enter a valid URL starting with http:// or https://'
  },
  title: {
    required: false,
    minLength: 3,
    maxLength: 100,
    message: 'Title must be between 3 and 100 characters'
  },
  description: {
    required: false,
    maxLength: 500,
    message: 'Description must not exceed 500 characters'
  },
  quality: {
    required: true,
    options: BROADCAST_QUALITIES,
    message: 'Please select a valid quality option'
  },
  language: {
    required: true,
    message: 'Please select a language'
  },
  fixtureId: {
    required: true,
    message: 'Please select a fixture'
  }
} as const;

/**
 * Broadcast link helper functions
 */
export const BroadcastHelpers = {
  /**
   * Validate broadcast link URL
   */
  isValidUrl: (url: string): boolean => {
    return BROADCAST_VALIDATION.url.pattern.test(url);
  },

  /**
   * Get quality badge color
   */
  getQualityColor: (quality: BroadcastQuality): string => {
    const colors = {
      HD: 'success',
      SD: 'warning', 
      Mobile: 'default'
    };
    return colors[quality];
  },

  /**
   * Get status badge color
   */
  getStatusColor: (status: BroadcastStatus): string => {
    const colors = {
      active: 'success',
      inactive: 'default',
      pending: 'processing',
      blocked: 'error'
    };
    return colors[status];
  },

  /**
   * Format view count
   */
  formatViewCount: (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  },

  /**
   * Get language display name
   */
  getLanguageDisplayName: (language: string): string => {
    const languageMap: Record<string, string> = {
      'en': 'English',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ar': 'Arabic',
      'ru': 'Russian',
      'zh': 'Chinese',
      'ja': 'Japanese',
      'ko': 'Korean'
    };
    return languageMap[language] || language;
  },

  /**
   * Generate broadcast link title from fixture
   */
  generateTitle: (fixture: { homeTeam: string; awayTeam: string; league: string }, quality: BroadcastQuality): string => {
    return `${fixture.homeTeam} vs ${fixture.awayTeam} - ${quality} Stream`;
  },

  /**
   * Check if broadcast link is live
   */
  isLive: (fixture: { date: string; status: string }): boolean => {
    return fixture.status === 'LIVE' || fixture.status === 'IN_PLAY';
  },

  /**
   * Get fixture display text
   */
  getFixtureDisplayText: (fixture: { homeTeam: string; awayTeam: string; date: string }): string => {
    const date = new Date(fixture.date).toLocaleDateString();
    return `${fixture.homeTeam} vs ${fixture.awayTeam} (${date})`;
  }
};

/**
 * Mock data for development
 */
export const MOCK_BROADCAST_LINKS: BroadcastLink[] = [
  {
    id: '1',
    fixtureId: 'fixture-1',
    fixture: {
      id: 'fixture-1',
      homeTeam: 'Manchester United',
      awayTeam: 'Liverpool',
      date: '2024-05-26T15:00:00Z',
      league: 'Premier League',
      status: 'SCHEDULED'
    },
    url: 'https://stream1.example.com/match1',
    title: 'Manchester United vs Liverpool - HD Stream',
    description: 'High quality stream for Premier League match',
    quality: 'HD',
    language: 'English',
    isActive: true,
    status: 'active',
    viewCount: 15420,
    rating: 4.5,
    createdBy: 'admin',
    createdAt: '2024-05-25T10:00:00Z',
    updatedAt: '2024-05-25T10:00:00Z',
    tags: ['premier-league', 'hd', 'english']
  },
  {
    id: '2',
    fixtureId: 'fixture-1',
    fixture: {
      id: 'fixture-1',
      homeTeam: 'Manchester United',
      awayTeam: 'Liverpool',
      date: '2024-05-26T15:00:00Z',
      league: 'Premier League',
      status: 'SCHEDULED'
    },
    url: 'https://stream2.example.com/match1-mobile',
    title: 'Manchester United vs Liverpool - Mobile Stream',
    description: 'Mobile optimized stream',
    quality: 'Mobile',
    language: 'English',
    isActive: true,
    status: 'active',
    viewCount: 8930,
    rating: 4.2,
    createdBy: 'editor1',
    createdAt: '2024-05-25T11:00:00Z',
    updatedAt: '2024-05-25T11:00:00Z',
    tags: ['premier-league', 'mobile', 'english']
  },
  {
    id: '3',
    fixtureId: 'fixture-2',
    fixture: {
      id: 'fixture-2',
      homeTeam: 'Barcelona',
      awayTeam: 'Real Madrid',
      date: '2024-05-27T20:00:00Z',
      league: 'La Liga',
      status: 'SCHEDULED'
    },
    url: 'https://stream3.example.com/clasico',
    title: 'El Clasico - HD Stream',
    description: 'Barcelona vs Real Madrid in HD',
    quality: 'HD',
    language: 'Spanish',
    isActive: false,
    status: 'pending',
    viewCount: 0,
    rating: 0,
    createdBy: 'editor2',
    createdAt: '2024-05-25T12:00:00Z',
    updatedAt: '2024-05-25T12:00:00Z',
    tags: ['la-liga', 'clasico', 'spanish']
  }
];
