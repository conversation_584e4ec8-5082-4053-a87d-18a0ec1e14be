/**
 * System Users List Page
 * Management page for SystemUser accounts (Admin/Editor/Moderator)
 */

'use client';

import React, { useState } from 'react';
import {
  Card,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Avatar,
  Dropdown,
  Modal,
  Typography,
  Alert,
  Table,
  Row,
  Col,
  Statistic,
  Breadcrumb,
} from 'antd';
import {
  UserOutlined,
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  TeamOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  ExclamationCircleOutlined,
  HomeOutlined,
} from '@ant-design/icons';
import { useUsers, useUserStatistics, useDeleteUser } from '@/hooks/api';
import {
  SystemUser,
  UserListParams,
  SystemUserRole,
  UserStatus,
  ROLE_LABELS,
  STATUS_LABELS,
  ROLE_COLORS,
  STATUS_COLORS,
  userHelpers,
  DEFAULT_USER_PARAMS,
} from '@/types/user';
import { useRouter } from 'next/navigation';

const { Text } = Typography;
const { confirm } = Modal;

export default function SystemUsersPage() {
  const router = useRouter();
  const [params, setParams] = useState<UserListParams>(DEFAULT_USER_PARAMS);

  // API hooks
  const { data: usersData, isLoading, error } = useUsers(params);
  const { data: statistics } = useUserStatistics();
  const deleteUserMutation = useDeleteUser();

  // Handle search
  const handleSearch = (value: string) => {
    setParams(prev => ({ ...prev, search: value, page: 1 }));
  };

  // Handle filter change
  const handleFilterChange = (key: keyof UserListParams, value: any) => {
    setParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle pagination
  const handlePageChange = (page: number, pageSize: number) => {
    setParams(prev => ({ ...prev, page, limit: pageSize }));
  };

  // Handle table change (pagination, filters, sorter)
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    // Handle pagination
    if (pagination) {
      setParams(prev => ({
        ...prev,
        page: pagination.current,
        limit: pagination.pageSize
      }));
    }

    // Handle sorting
    if (sorter && sorter.field) {
      setParams(prev => ({
        ...prev,
        sortBy: sorter.field as any,
        sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'
      }));
    }
  };

  // Handle delete user
  const handleDeleteUser = (user: SystemUser) => {
    confirm({
      title: 'Delete User',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>Are you sure you want to delete user <strong>{userHelpers.getDisplayName(user)}</strong>?</p>
          <p>This action cannot be undone.</p>
        </div>
      ),
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => deleteUserMutation.mutate(user.id),
    });
  };

  // Table columns
  const columns = [
    {
      title: 'User',
      dataIndex: 'username',
      key: 'username',
      sorter: true,
      render: (username: string, user: SystemUser) => {
        const avatar = userHelpers.getAvatarDisplay(user);
        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Avatar
              size="default"
              src={avatar.type === 'url' ? avatar.value : undefined}
              style={avatar.type === 'initials' ? { backgroundColor: ROLE_COLORS[user.role] } : undefined}
            >
              {avatar.type === 'initials' ? avatar.value : undefined}
            </Avatar>
            <div>
              <div style={{ fontWeight: 'bold' }}>{userHelpers.getFullName(user)}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>@{username}</div>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      sorter: true,
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      sorter: true,
      render: (role: SystemUserRole) => (
        <Tag color={ROLE_COLORS[role]}>
          {ROLE_LABELS[role]}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      sorter: true,
      render: (status: UserStatus) => (
        <Tag color={STATUS_COLORS[status]}>
          {STATUS_LABELS[status]}
        </Tag>
      ),
    },
    {
      title: 'Last Login',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      sorter: true,
      render: (lastLogin: string) => (
        <Text type="secondary">{userHelpers.formatLastLogin(lastLogin)}</Text>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: true,
      render: (createdAt: string) => (
        <Text type="secondary">{new Date(createdAt).toLocaleDateString()}</Text>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, user: SystemUser) => {
        const menuItems = [
          {
            key: 'edit',
            icon: <EditOutlined />,
            label: 'Edit User',
            onClick: () => router.push(`/users/system/${user.id}/edit`),
          },
          {
            key: 'profile',
            icon: <UserOutlined />,
            label: 'View Profile',
            onClick: () => router.push(`/users/system/${user.id}`),
          },
          {
            type: 'divider' as const,
          },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: 'Delete User',
            danger: true,
            onClick: () => handleDeleteUser(user),
          },
        ];

        return (
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        );
      },
    },
  ];

  return (
    <div>
      {/* Breadcrumb Navigation */}
      <Breadcrumb
        className="mb-4"
        items={[
          {
            href: '/',
            title: <HomeOutlined />
          },
          {
            href: '/users',
            title: 'User System'
          },
          {
            title: 'System Users'
          }
        ]}
      />

      {/* Page Header */}
      <div className="mb-6 flex justify-between items-start">
        <div>
          <Typography.Title level={2}>
            <UserOutlined className="mr-2" />
            System Users
          </Typography.Title>
          <Typography.Text type="secondary">
            Manage administrator, editor, and moderator accounts
          </Typography.Text>
        </div>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => router.push('/users/system/create')}
          >
            Create User
          </Button>
        </Space>
      </div>
      {/* Statistics Cards */}
      {statistics && (
        <Row gutter={16} className="mb-6">
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="Total Users"
                value={statistics.total}
                prefix={<TeamOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="Active Users"
                value={statistics.active}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="Recent Logins"
                value={statistics.recentLogins}
                prefix={<UserAddOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="New This Month"
                value={statistics.newThisMonth}
                prefix={<UserAddOutlined />}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Filters and Search */}
      <Card style={{ marginBottom: '24px' }}>
        <Space size="middle" wrap>
          <Input
            placeholder="Search users..."
            prefix={<SearchOutlined />}
            value={params.search}
            onChange={(e) => handleSearch(e.target.value)}
            style={{ width: '300px' }}
            allowClear
          />

          <Select
            placeholder="Filter by role"
            value={params.role}
            onChange={(value) => handleFilterChange('role', value)}
            style={{ width: '150px' }}
            allowClear
          >
            <Select.Option value="admin">Administrator</Select.Option>
            <Select.Option value="editor">Editor</Select.Option>
            <Select.Option value="moderator">Moderator</Select.Option>
          </Select>

          <Select
            placeholder="Filter by status"
            value={params.status}
            onChange={(value) => handleFilterChange('status', value)}
            style={{ width: '150px' }}
            allowClear
          >
            <Select.Option value="active">Active</Select.Option>
            <Select.Option value="inactive">Inactive</Select.Option>
            <Select.Option value="suspended">Suspended</Select.Option>
          </Select>
        </Space>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert
          message="Error Loading Users"
          description="Failed to load user data. Please try again."
          type="error"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* Users Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={usersData?.users || []}
          loading={isLoading}
          pagination={{
            current: params.page,
            pageSize: params.limit,
            total: usersData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} users`,
          }}
          onChange={handleTableChange}
          rowKey="id"
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  );
}
