'use client';

import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Divider,
  Alert,
  Tag,
  Table,
  Modal,
  message,
  Row,
  Col,
  Statistic,
  Progress
} from 'antd';
import {
  PlayCircleOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  LinkOutlined,
  GlobalOutlined,
  StarOutlined,
  ExperimentOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ColumnsType } from 'antd/es/table';
import {
  BroadcastForm,
  BroadcastLink,
  CreateBroadcastLinkRequest,
  MOCK_BROADCAST_LINKS,
  BroadcastHelpers
} from '@/components/broadcast';

const { Title, Text, Paragraph } = Typography;

// Mock fixtures for demo
const DEMO_FIXTURES = [
  {
    id: 'demo-fixture-1',
    homeTeam: 'Arsenal',
    awayTeam: 'Chelsea',
    date: '2024-05-30T16:00:00Z',
    league: 'Premier League',
    status: 'SCHEDULED'
  },
  {
    id: 'demo-fixture-2',
    homeTeam: 'Inter Milan',
    awayTeam: 'Napoli',
    date: '2024-05-31T19:00:00Z',
    league: 'Serie A',
    status: 'LIVE'
  }
];

export default function BroadcastDemoPage() {
  const router = useRouter();
  const [broadcastLinks, setBroadcastLinks] = useState<BroadcastLink[]>(MOCK_BROADCAST_LINKS);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(false);

  // Statistics calculation
  const statistics = React.useMemo(() => {
    return {
      total: broadcastLinks.length,
      active: broadcastLinks.filter(l => l.isActive).length,
      hd: broadcastLinks.filter(l => l.quality === 'HD').length,
      totalViews: broadcastLinks.reduce((sum, l) => sum + (l.viewCount || 0), 0),
      avgRating: broadcastLinks.reduce((sum, l) => sum + (l.rating || 0), 0) / broadcastLinks.length
    };
  }, [broadcastLinks]);

  // Handle create broadcast link
  const handleCreateLink = async (data: CreateBroadcastLinkRequest) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newLink: BroadcastLink = {
        id: `demo-${Date.now()}`,
        fixtureId: data.fixtureId,
        fixture: DEMO_FIXTURES.find(f => f.id === data.fixtureId),
        url: data.url,
        title: data.title || 'Demo Stream',
        description: data.description,
        quality: data.quality,
        language: data.language,
        isActive: true,
        status: 'active',
        viewCount: 0,
        rating: 0,
        createdBy: 'demo-user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: data.tags || []
      };

      setBroadcastLinks(prev => [newLink, ...prev]);
      setShowCreateModal(false);
      message.success('Demo broadcast link created successfully!');
    } catch (error) {
      message.error('Failed to create broadcast link');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete
  const handleDelete = (id: string) => {
    setBroadcastLinks(prev => prev.filter(link => link.id !== id));
    message.success('Broadcast link deleted successfully');
  };

  // Table columns
  const columns: ColumnsType<BroadcastLink> = [
    {
      title: 'Fixture',
      key: 'fixture',
      render: (_, record) => (
        <div>
          <Text strong>
            {record.fixture ? BroadcastHelpers.getFixtureDisplayText(record.fixture) : 'Demo Fixture'}
          </Text>
          <br />
          <Text type="secondary" className="text-sm">
            {record.fixture?.league || 'Demo League'}
          </Text>
        </div>
      ),
      width: 200,
    },
    {
      title: 'Stream',
      key: 'stream',
      render: (_, record) => (
        <div>
          <div className="flex items-center gap-2 mb-1">
            <LinkOutlined />
            <Text>{record.title}</Text>
          </div>
          <div className="flex items-center gap-2">
            <Tag color={BroadcastHelpers.getQualityColor(record.quality)}>
              {record.quality}
            </Tag>
            <Tag icon={<GlobalOutlined />}>
              {record.language}
            </Tag>
          </div>
        </div>
      ),
      width: 180,
    },
    {
      title: 'Performance',
      key: 'performance',
      render: (_, record) => (
        <div>
          <div className="flex items-center gap-2">
            <EyeOutlined />
            <Text>{BroadcastHelpers.formatViewCount(record.viewCount || 0)}</Text>
          </div>
          {record.rating && (
            <div className="flex items-center gap-2 mt-1">
              <StarOutlined />
              <Text>{record.rating.toFixed(1)}</Text>
            </div>
          )}
        </div>
      ),
      width: 120,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => message.info(`Viewing details for ${record.title}`)}
          />
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => message.info(`Editing ${record.title}`)}
          />
          <Button
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
      width: 120,
    },
  ];

  return (
    <div>
      {/* Page Header */}
      <div className="mb-6">
        <Title level={2}>
          <ExperimentOutlined className="mr-2" />
          Broadcast Management Demo
        </Title>
        <Text type="secondary">
          Interactive demonstration of broadcast link management features
        </Text>
      </div>

      {/* Demo Notice */}
      <Alert
        message="Demo Mode"
        description="This is a demonstration page showing broadcast management functionality. All data is simulated and changes won't be persisted."
        type="info"
        showIcon
        className="mb-6"
      />

      {/* Statistics Cards */}
      <Row gutter={16} className="mb-6">
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Total Links"
              value={statistics.total}
              prefix={<LinkOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Active Links"
              value={statistics.active}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="HD Quality"
              value={statistics.hd}
              suffix={`/ ${statistics.total}`}
              valueStyle={{ color: '#1890ff' }}
            />
            <Progress
              percent={(statistics.hd / statistics.total) * 100}
              size="small"
              showInfo={false}
              className="mt-2"
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="Avg Rating"
              value={statistics.avgRating}
              precision={1}
              prefix={<StarOutlined />}
              suffix="/ 5.0"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Feature Demonstration */}
      <Row gutter={16} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="🚀 Quick Actions" className="h-full">
            <Space direction="vertical" className="w-full">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setShowCreateModal(true)}
                block
              >
                Create Demo Broadcast Link
              </Button>
              <Button
                icon={<PlayCircleOutlined />}
                onClick={() => router.push('/broadcast-links')}
                block
              >
                View Full Broadcast Management
              </Button>
              <Button
                icon={<LinkOutlined />}
                onClick={() => message.info('Testing stream links...')}
                block
              >
                Test All Stream Links
              </Button>
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="📊 Features Overview" className="h-full">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Tag color="green">✓</Tag>
                <Text>CRUD operations for broadcast links</Text>
              </div>
              <div className="flex items-center gap-2">
                <Tag color="green">✓</Tag>
                <Text>Quality control (HD/SD/Mobile)</Text>
              </div>
              <div className="flex items-center gap-2">
                <Tag color="green">✓</Tag>
                <Text>Multi-language support</Text>
              </div>
              <div className="flex items-center gap-2">
                <Tag color="green">✓</Tag>
                <Text>Fixture-based organization</Text>
              </div>
              <div className="flex items-center gap-2">
                <Tag color="green">✓</Tag>
                <Text>Performance tracking</Text>
              </div>
              <div className="flex items-center gap-2">
                <Tag color="green">✓</Tag>
                <Text>Status management</Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Broadcast Links Table */}
      <Card title="Current Broadcast Links" className="mb-6">
        <Table
          columns={columns}
          dataSource={broadcastLinks}
          rowKey="id"
          pagination={{
            pageSize: 5,
            showSizeChanger: false,
            showQuickJumper: false,
          }}
          size="small"
        />
      </Card>

      {/* API Integration Info */}
      <Card title="🔗 API Integration">
        <Paragraph>
          The broadcast management system integrates with the following API endpoints:
        </Paragraph>
        <div className="bg-gray-50 p-4 rounded">
          <Text code>GET /api/broadcast-links</Text> - List all broadcast links<br />
          <Text code>POST /api/broadcast-links</Text> - Create new broadcast link<br />
          <Text code>GET /api/broadcast-links/:id</Text> - Get specific broadcast link<br />
          <Text code>PUT /api/broadcast-links/:id</Text> - Update broadcast link<br />
          <Text code>DELETE /api/broadcast-links/:id</Text> - Delete broadcast link<br />
          <Text code>GET /api/broadcast-links/fixture/:id</Text> - Get links by fixture
        </div>
      </Card>

      {/* Create Modal */}
      <Modal
        title="Create Demo Broadcast Link"
        open={showCreateModal}
        onCancel={() => setShowCreateModal(false)}
        footer={null}
        width={800}
      >
        <BroadcastForm
          mode="create"
          onSubmit={handleCreateLink}
          onCancel={() => setShowCreateModal(false)}
          loading={loading}
          fixtures={DEMO_FIXTURES}
        />
      </Modal>
    </div>
  );
}
