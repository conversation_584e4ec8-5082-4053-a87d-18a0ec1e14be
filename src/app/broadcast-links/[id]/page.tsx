'use client';

import React from 'react';
import {
  Card,
  <PERSON>readcrumb,
  Typography,
  Button,
  Space,
  Tag,
  Descriptions,
  Alert,
  Spin,
  Row,
  Col,
  Statistic,
  Tooltip,
  Popconfirm,
  message
} from 'antd';
import {
  HomeOutlined,
  PlayCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  <PERSON>Outlined,
  EyeOutlined,
  GlobalOutlined,
  CalendarOutlined,
  UserOutlined,
  StarOutlined,
  TagsOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { MOCK_BROADCAST_LINKS, BroadcastHelpers } from '@/types/broadcast';
import { useBroadcastLink, useDeleteBroadcastLink } from '@/hooks/api/broadcast-hooks';

const { Title, Text, Paragraph } = Typography;

export default function BroadcastLinkDetailPage() {
  const router = useRouter();
  const params = useParams();
  const linkId = params.id as string;

  // For development, use mock data
  const broadcastLinkQuery = {
    data: MOCK_BROADCAST_LINKS.find(link => link.id === linkId),
    isLoading: false,
    error: null
  };

  const deleteLink = useDeleteBroadcastLink();

  const handleDelete = async () => {
    try {
      await deleteLink.mutateAsync(linkId);
      message.success('Broadcast link deleted successfully');
      router.push('/broadcast-links');
    } catch (error) {
      message.error('Failed to delete broadcast link');
    }
  };

  const handleEdit = () => {
    router.push(`/broadcast-links/${linkId}/edit`);
  };

  // Loading state
  if (broadcastLinkQuery.isLoading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </div>
    );
  }

  // Error state
  if (broadcastLinkQuery.error) {
    return (
      <div className="p-6">
        <Alert
          message="Error Loading Broadcast Link"
          description="Failed to load broadcast link data. Please try again."
          type="error"
          showIcon
        />
      </div>
    );
  }

  // Not found state
  if (!broadcastLinkQuery.data) {
    return (
      <div className="p-6">
        <Alert
          message="Broadcast Link Not Found"
          description="The requested broadcast link could not be found."
          type="warning"
          showIcon
          action={
            <Link href="/broadcast-links">
              <Button type="primary">Back to Broadcast Links</Button>
            </Link>
          }
        />
      </div>
    );
  }

  const broadcastLink = broadcastLinkQuery.data;
  const fixture = broadcastLink.fixture;

  return (
    <div>
      {/* Breadcrumb Navigation */}
      <Breadcrumb
        className="mb-4"
        items={[
          {
            href: '/',
            title: <HomeOutlined />
          },
          {
            href: '/broadcast-links',
            title: (
              <>
                <PlayCircleOutlined />
                <span className="ml-1">Broadcast Links</span>
              </>
            )
          },
          {
            title: broadcastLink.title || 'Broadcast Link Details'
          }
        ]}
      />

      {/* Page Header */}
      <div className="mb-6 flex justify-between items-start">
        <div>
          <Title level={2}>
            <PlayCircleOutlined className="mr-2" />
            {broadcastLink.title || 'Broadcast Link Details'}
          </Title>
          <Text type="secondary">
            Detailed information about this broadcast link
          </Text>
        </div>
        <Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={handleEdit}
          >
            Edit
          </Button>
          <Popconfirm
            title="Delete Broadcast Link"
            description="Are you sure you want to delete this broadcast link? This action cannot be undone."
            onConfirm={handleDelete}
            okText="Yes, Delete"
            cancelText="Cancel"
            okButtonProps={{ danger: true }}
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              loading={deleteLink.isPending}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      </div>

      {/* Status Alert */}
      {!broadcastLink.isActive && (
        <Alert
          message="Inactive Broadcast Link"
          description="This broadcast link is currently inactive and not visible to users."
          type="warning"
          showIcon
          className="mb-6"
        />
      )}

      {/* Live Status */}
      {fixture && BroadcastHelpers.isLive(fixture) && (
        <Alert
          message="Live Match"
          description="This broadcast link is for a currently live match."
          type="success"
          showIcon
          className="mb-6"
        />
      )}

      <Row gutter={24}>
        {/* Main Information */}
        <Col xs={24} lg={16}>
          {/* Fixture Information */}
          {fixture && (
            <Card title="Fixture Information" className="mb-6">
              <Descriptions column={1}>
                <Descriptions.Item label="Match">
                  <Text strong className="text-lg">
                    {fixture.homeTeam} vs {fixture.awayTeam}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="League">
                  {fixture.league}
                </Descriptions.Item>
                <Descriptions.Item label="Date & Time">
                  <CalendarOutlined className="mr-2" />
                  {new Date(fixture.date).toLocaleString()}
                </Descriptions.Item>
                <Descriptions.Item label="Status">
                  <Tag color={BroadcastHelpers.isLive(fixture) ? 'red' : 'blue'}>
                    {fixture.status}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          )}

          {/* Stream Information */}
          <Card title="Stream Information" className="mb-6">
            <Descriptions column={1}>
              <Descriptions.Item label="Stream URL">
                <div className="flex items-center gap-2">
                  <Text code className="break-all">{broadcastLink.url}</Text>
                  <Tooltip title="Open stream in new tab">
                    <Button
                      type="link"
                      size="small"
                      icon={<ShareAltOutlined />}
                      onClick={() => window.open(broadcastLink.url, '_blank')}
                    />
                  </Tooltip>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="Quality">
                <Tag color={BroadcastHelpers.getQualityColor(broadcastLink.quality)}>
                  {broadcastLink.quality}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Language">
                <Tag icon={<GlobalOutlined />}>
                  {broadcastLink.language}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={BroadcastHelpers.getStatusColor(broadcastLink.status)}>
                  {broadcastLink.status.toUpperCase()}
                </Tag>
                <Tag color={broadcastLink.isActive ? 'success' : 'default'} className="ml-2">
                  {broadcastLink.isActive ? 'Active' : 'Inactive'}
                </Tag>
              </Descriptions.Item>
              {broadcastLink.description && (
                <Descriptions.Item label="Description">
                  <Paragraph>{broadcastLink.description}</Paragraph>
                </Descriptions.Item>
              )}
              {broadcastLink.tags && broadcastLink.tags.length > 0 && (
                <Descriptions.Item label="Tags">
                  <Space wrap>
                    {broadcastLink.tags.map(tag => (
                      <Tag key={tag} icon={<TagsOutlined />}>
                        {tag}
                      </Tag>
                    ))}
                  </Space>
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>

          {/* Metadata */}
          <Card title="Metadata">
            <Descriptions column={2}>
              <Descriptions.Item label="Created By">
                <UserOutlined className="mr-2" />
                {broadcastLink.createdBy}
              </Descriptions.Item>
              <Descriptions.Item label="Created At">
                {new Date(broadcastLink.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="Last Updated">
                {new Date(broadcastLink.updatedAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="Link ID">
                <Text code>{broadcastLink.id}</Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* Statistics Sidebar */}
        <Col xs={24} lg={8}>
          {/* Performance Stats */}
          <Card title="Performance Statistics" className="mb-6">
            <Space direction="vertical" className="w-full">
              <Statistic
                title="Total Views"
                value={broadcastLink.viewCount || 0}
                prefix={<EyeOutlined />}
                formatter={(value) => BroadcastHelpers.formatViewCount(Number(value))}
              />
              {broadcastLink.rating && (
                <Statistic
                  title="User Rating"
                  value={broadcastLink.rating}
                  precision={1}
                  prefix={<StarOutlined />}
                  suffix="/ 5.0"
                  valueStyle={{ color: '#faad14' }}
                />
              )}
            </Space>
          </Card>

          {/* Quick Actions */}
          <Card title="Quick Actions">
            <Space direction="vertical" className="w-full">
              <Button
                block
                icon={<LinkOutlined />}
                onClick={() => window.open(broadcastLink.url, '_blank')}
              >
                Test Stream Link
              </Button>
              <Button
                block
                icon={<EditOutlined />}
                onClick={handleEdit}
              >
                Edit Details
              </Button>
              <Button
                block
                icon={<PlayCircleOutlined />}
                onClick={() => router.push('/broadcast-links')}
              >
                View All Links
              </Button>
            </Space>
          </Card>

          {/* Related Information */}
          {fixture && (
            <Card title="Related Information">
              <Space direction="vertical" className="w-full">
                <Text>
                  <strong>Fixture ID:</strong> {fixture.id}
                </Text>
                <Text>
                  <strong>League:</strong> {fixture.league}
                </Text>
                <Button
                  block
                  type="dashed"
                  onClick={() => message.info('Fixture details coming soon')}
                >
                  View Fixture Details
                </Button>
              </Space>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
}
