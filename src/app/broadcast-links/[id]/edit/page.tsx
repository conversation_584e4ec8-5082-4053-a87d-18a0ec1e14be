'use client';

import React from 'react';
import { Card, Breadcrumb, Typography, message, Spin, Alert } from 'antd';
import { HomeOutlined, PlayCircleOutlined, EditOutlined } from '@ant-design/icons';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { BroadcastForm } from '@/components/broadcast';
import { UpdateBroadcastLinkRequest, MOCK_BROADCAST_LINKS } from '@/types/broadcast';
import { useBroadcastLink, useUpdateBroadcastLink } from '@/hooks/api/broadcast-hooks';

const { Title, Text } = Typography;

export default function EditBroadcastLinkPage() {
  const router = useRouter();
  const params = useParams();
  const linkId = params.id as string;

  // For development, use mock data
  const broadcastLinkQuery = {
    data: MOCK_BROADCAST_LINKS.find(link => link.id === linkId),
    isLoading: false,
    error: null
  };

  const updateBroadcastLink = useUpdateBroadcastLink();

  const handleSubmit = async (data: UpdateBroadcastLinkRequest) => {
    try {
      await updateBroadcastLink.mutateAsync({ id: linkId, data });
      message.success('Broadcast link updated successfully!');
      router.push('/broadcast-links');
    } catch (error) {
      message.error('Failed to update broadcast link');
      throw error; // Re-throw to let form handle the error
    }
  };

  const handleCancel = () => {
    router.push('/broadcast-links');
  };

  // Loading state
  if (broadcastLinkQuery.isLoading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </div>
    );
  }

  // Error state
  if (broadcastLinkQuery.error) {
    return (
      <div className="p-6">
        <Alert
          message="Error Loading Broadcast Link"
          description="Failed to load broadcast link data. Please try again."
          type="error"
          showIcon
        />
      </div>
    );
  }

  // Not found state
  if (!broadcastLinkQuery.data) {
    return (
      <div className="p-6">
        <Alert
          message="Broadcast Link Not Found"
          description="The requested broadcast link could not be found."
          type="warning"
          showIcon
          action={
            <Link href="/broadcast-links">
              <button className="ant-btn ant-btn-primary">
                Back to Broadcast Links
              </button>
            </Link>
          }
        />
      </div>
    );
  }

  const broadcastLink = broadcastLinkQuery.data;

  return (
    <div>
      {/* Breadcrumb Navigation */}
      <Breadcrumb
        className="mb-4"
        items={[
          {
            href: '/',
            title: <HomeOutlined />
          },
          {
            href: '/broadcast-links',
            title: (
              <>
                <PlayCircleOutlined />
                <span className="ml-1">Broadcast Links</span>
              </>
            )
          },
          {
            href: `/broadcast-links/${linkId}`,
            title: broadcastLink.title || 'Broadcast Link'
          },
          {
            title: (
              <>
                <EditOutlined />
                <span className="ml-1">Edit</span>
              </>
            )
          }
        ]}
      />

      {/* Page Header */}
      <div className="mb-6">
        <Title level={2}>
          <EditOutlined className="mr-2" />
          Edit Broadcast Link
        </Title>
        <Text type="secondary">
          Update broadcast link information, quality settings, and status
        </Text>
      </div>

      {/* Current Link Info */}
      <Card className="mb-6 max-w-4xl" title="Current Broadcast Link">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Text strong>Fixture:</Text>
            <br />
            <Text>
              {broadcastLink.fixture
                ? `${broadcastLink.fixture.homeTeam} vs ${broadcastLink.fixture.awayTeam}`
                : 'Unknown Fixture'
              }
            </Text>
          </div>
          <div>
            <Text strong>Current URL:</Text>
            <br />
            <Text code className="break-all">{broadcastLink.url}</Text>
          </div>
          <div>
            <Text strong>Quality:</Text>
            <br />
            <Text>{broadcastLink.quality}</Text>
          </div>
          <div>
            <Text strong>Language:</Text>
            <br />
            <Text>{broadcastLink.language}</Text>
          </div>
          <div>
            <Text strong>Status:</Text>
            <br />
            <Text>{broadcastLink.isActive ? 'Active' : 'Inactive'}</Text>
          </div>
          <div>
            <Text strong>Created:</Text>
            <br />
            <Text>{new Date(broadcastLink.createdAt).toLocaleString()}</Text>
          </div>
        </div>
      </Card>

      {/* Edit Form */}
      <div className="max-w-4xl">
        <BroadcastForm
          mode="edit"
          initialData={broadcastLink}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={updateBroadcastLink.isPending}
        />
      </div>

      {/* Edit Guidelines */}
      <Card className="mt-6 max-w-4xl" title="📝 Editing Guidelines">
        <div className="space-y-4">
          <div>
            <Title level={5}>What You Can Edit:</Title>
            <ul className="list-disc list-inside text-gray-600">
              <li>Stream URL (if the link has changed)</li>
              <li>Title and description</li>
              <li>Quality setting (if stream quality changed)</li>
              <li>Language (if commentary language changed)</li>
              <li>Active status (enable/disable the link)</li>
              <li>Tags for better organization</li>
            </ul>
          </div>

          <div>
            <Title level={5}>Important Notes:</Title>
            <ul className="list-disc list-inside text-gray-600">
              <li>The fixture cannot be changed after creation</li>
              <li>Always test the URL after making changes</li>
              <li>Disabling a link will hide it from public view</li>
              <li>Changes are logged for audit purposes</li>
            </ul>
          </div>

          <div>
            <Title level={5}>Quality Changes:</Title>
            <ul className="list-disc list-inside text-gray-600">
              <li>Only change quality if the actual stream quality changed</li>
              <li>HD should only be used for 720p+ streams</li>
              <li>Mobile quality is for mobile-optimized streams</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
}
