'use client';

import React from 'react';
import { Card, Breadcrumb, Typography, message } from 'antd';
import { HomeOutlined, PlayCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { BroadcastForm } from '@/components/broadcast';
import { CreateBroadcastLinkRequest } from '@/types/broadcast';
import { useCreateBroadcastLink } from '@/hooks/api/broadcast-hooks';

const { Title, Text } = Typography;

// Mock fixtures data for development
const MOCK_FIXTURES = [
  {
    id: 'fixture-1',
    homeTeam: 'Manchester United',
    awayTeam: 'Liverpool',
    date: '2024-05-26T15:00:00Z',
    league: 'Premier League',
    status: 'SCHEDULED'
  },
  {
    id: 'fixture-2',
    homeTeam: 'Barcelona',
    awayTeam: 'Real Madrid',
    date: '2024-05-27T20:00:00Z',
    league: 'La Liga',
    status: 'SCHEDULED'
  },
  {
    id: 'fixture-3',
    homeTeam: 'Bayern Munich',
    awayTeam: 'Borussia Dortmund',
    date: '2024-05-28T18:30:00Z',
    league: 'Bundesliga',
    status: 'LIVE'
  },
  {
    id: 'fixture-4',
    homeTeam: 'PSG',
    awayTeam: 'Marseille',
    date: '2024-05-29T21:00:00Z',
    league: 'Ligue 1',
    status: 'SCHEDULED'
  },
  {
    id: 'fixture-5',
    homeTeam: 'Juventus',
    awayTeam: 'AC Milan',
    date: '2024-05-30T19:45:00Z',
    league: 'Serie A',
    status: 'SCHEDULED'
  }
];

export default function CreateBroadcastLinkPage() {
  const router = useRouter();
  const createBroadcastLink = useCreateBroadcastLink();

  const handleSubmit = async (data: CreateBroadcastLinkRequest) => {
    try {
      await createBroadcastLink.mutateAsync(data);
      message.success('Broadcast link created successfully!');
      router.push('/broadcast-links');
    } catch (error) {
      message.error('Failed to create broadcast link');
      throw error; // Re-throw to let form handle the error
    }
  };

  const handleCancel = () => {
    router.push('/broadcast-links');
  };

  return (
    <div>
      {/* Breadcrumb Navigation */}
      <Breadcrumb
        className="mb-4"
        items={[
          {
            href: '/',
            title: <HomeOutlined />
          },
          {
            href: '/broadcast-links',
            title: (
              <>
                <PlayCircleOutlined />
                <span className="ml-1">Broadcast Links</span>
              </>
            )
          },
          {
            title: (
              <>
                <PlusOutlined />
                <span className="ml-1">Create New</span>
              </>
            )
          }
        ]}
      />

      {/* Page Header */}
      <div className="mb-6">
        <Title level={2}>
          <PlusOutlined className="mr-2" />
          Create Broadcast Link
        </Title>
        <Text type="secondary">
          Add a new broadcast link for a football fixture with quality and language settings
        </Text>
      </div>

      {/* Create Form */}
      <div className="max-w-4xl">
        <BroadcastForm
          mode="create"
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={createBroadcastLink.isPending}
          fixtures={MOCK_FIXTURES}
        />
      </div>

      {/* Help Information */}
      <Card className="mt-6 max-w-4xl" title="📋 Guidelines for Creating Broadcast Links">
        <div className="space-y-4">
          <div>
            <Title level={5}>URL Requirements:</Title>
            <ul className="list-disc list-inside text-gray-600">
              <li>Must start with http:// or https://</li>
              <li>Should be a direct link to the stream</li>
              <li>Avoid shortened URLs when possible</li>
              <li>Test the URL before submitting</li>
            </ul>
          </div>

          <div>
            <Title level={5}>Quality Guidelines:</Title>
            <ul className="list-disc list-inside text-gray-600">
              <li><strong>HD:</strong> 720p or higher resolution streams</li>
              <li><strong>SD:</strong> 480p standard definition streams</li>
              <li><strong>Mobile:</strong> Optimized for mobile devices</li>
            </ul>
          </div>

          <div>
            <Title level={5}>Language Selection:</Title>
            <ul className="list-disc list-inside text-gray-600">
              <li>Choose the primary commentary language</li>
              <li>Select "Other" if the language is not listed</li>
              <li>Multiple language streams require separate entries</li>
            </ul>
          </div>

          <div>
            <Title level={5}>Best Practices:</Title>
            <ul className="list-disc list-inside text-gray-600">
              <li>Use descriptive titles that include team names</li>
              <li>Add relevant tags for better organization</li>
              <li>Include quality and language in the description</li>
              <li>Verify stream availability before publishing</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
}
