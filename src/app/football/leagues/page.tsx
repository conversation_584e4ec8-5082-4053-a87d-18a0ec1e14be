/**
 * Football Leagues Management Page
 * Comprehensive leagues management with CRUD operations
 */

'use client';

import React, { useState } from 'react';
import {
  Card,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Avatar,
  Dropdown,
  Modal,
  Typography,
  Alert,
  Image,
  Table,
  Row,
  Col,
  Statistic,
  Breadcrumb,
  message
} from 'antd';
import {
  TrophyOutlined,
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  GlobalOutlined,
  CalendarOutlined,
  TeamOutlined,
  SyncOutlined,
  ExclamationCircleOutlined,
  HomeOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useLeagues } from '@/hooks/api';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

const { Text } = Typography;
const { confirm } = Modal;

// Types for leagues
interface League {
  id: number;
  name: string;
  country: string;
  logo?: string;
  flag?: string;
  season: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  teamsCount?: number;
  fixturesCount?: number;
}

interface LeagueListParams {
  page?: number;
  limit?: number;
  search?: string;
  country?: string;
  season?: number;
  isActive?: boolean;
  sortBy?: 'name' | 'country' | 'season' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

const DEFAULT_PARAMS: LeagueListParams = {
  page: 1,
  limit: 20,
  sortBy: 'name',
  sortOrder: 'asc',
};

export default function LeaguesPage() {
  const router = useRouter();
  const [params, setParams] = useState<LeagueListParams>(DEFAULT_PARAMS);

  // API hooks
  const { data: leaguesData, isLoading, error } = useLeagues(params);

  // Mock statistics
  const statistics = {
    total: 45,
    active: 38,
    inactive: 7,
    countries: 15,
    currentSeason: 2024,
  };

  // Handle search
  const handleSearch = (value: string) => {
    setParams(prev => ({ ...prev, search: value, page: 1 }));
  };

  // Handle filter change
  const handleFilterChange = (key: keyof LeagueListParams, value: any) => {
    setParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle table change
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    if (pagination) {
      setParams(prev => ({
        ...prev,
        page: pagination.current,
        limit: pagination.pageSize
      }));
    }

    if (sorter && sorter.field) {
      setParams(prev => ({
        ...prev,
        sortBy: sorter.field as any,
        sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'
      }));
    }
  };

  // Handle delete league
  const handleDeleteLeague = (league: League) => {
    confirm({
      title: 'Delete League',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>Are you sure you want to delete league <strong>{league.name}</strong>?</p>
          <p>This action cannot be undone and will affect all related teams and fixtures.</p>
        </div>
      ),
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        console.log('Delete league:', league.id);
        // TODO: Implement delete mutation
      },
    });
  };

  // Table columns
  const columns = [
    {
      title: 'League',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      render: (name: string, league: League) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {league.logo ? (
            <Image
              src={league.logo}
              alt={name}
              width={32}
              height={32}
              style={{ borderRadius: '4px' }}
              fallback="/images/league-placeholder.png"
            />
          ) : (
            <Avatar size="default" icon={<TrophyOutlined />} />
          )}
          <div>
            <div style={{ fontWeight: 'bold' }}>{name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              Season {league.season}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Country',
      dataIndex: 'country',
      key: 'country',
      sorter: true,
      render: (country: string, league: League) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {league.flag && (
            <Image
              src={league.flag}
              alt={country}
              width={20}
              height={15}
              style={{ borderRadius: '2px' }}
            />
          )}
          <span>{country}</span>
        </div>
      ),
    },
    {
      title: 'Season',
      dataIndex: 'season',
      key: 'season',
      sorter: true,
      render: (season: number) => (
        <Tag icon={<CalendarOutlined />} color="blue">
          {season}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Teams',
      dataIndex: 'teamsCount',
      key: 'teamsCount',
      render: (count: number = 0) => (
        <Tag icon={<TeamOutlined />} color="orange">
          {count} teams
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: true,
      render: (createdAt: string) => (
        <Text type="secondary">{new Date(createdAt).toLocaleDateString()}</Text>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, league: League) => {
        const menuItems = [
          {
            key: 'view',
            icon: <TrophyOutlined />,
            label: 'View Details',
            onClick: () => router.push(`/football/leagues/${league.id}`),
          },
          {
            key: 'edit',
            icon: <EditOutlined />,
            label: 'Edit League',
            onClick: () => router.push(`/football/leagues/${league.id}/edit`),
          },
          {
            key: 'teams',
            icon: <TeamOutlined />,
            label: 'View Teams',
            onClick: () => router.push(`/football/teams?league=${league.id}`),
          },
          {
            type: 'divider' as const,
          },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: 'Delete League',
            danger: true,
            onClick: () => handleDeleteLeague(league),
          },
        ];

        return (
          <Dropdown menu={{ items: menuItems }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        );
      },
    },
  ];

  return (
    <AppLayout>
      <PageHeader
        title="Football Leagues"
        subtitle="Manage football leagues and competitions"
        breadcrumbs={[
          { title: 'Home', href: '/' },
          { title: 'Football', href: '/football' },
          { title: 'Leagues' },
        ]}
        actions={[
          <Button
            key="sync"
            icon={<SyncOutlined />}
            onClick={() => router.push('/football/sync')}
          >
            Sync Data
          </Button>,
          <Button
            key="create"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => router.push('/football/leagues/create')}
          >
            Add League
          </Button>,
        ]}
      />

      <Container>
        {/* Statistics Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '24px'
        }}>
          <StatCard
            title="Total Leagues"
            value={statistics.total}
            subtitle="All leagues"
            icon={<TrophyOutlined />}
          />

          <StatCard
            title="Active Leagues"
            value={statistics.active}
            subtitle="Currently active"
            icon={<GlobalOutlined />}
            trend={{ value: Math.round((statistics.active / statistics.total) * 100), isPositive: true }}
          />

          <StatCard
            title="Countries"
            value={statistics.countries}
            subtitle="Different countries"
            icon={<GlobalOutlined />}
          />

          <StatCard
            title="Current Season"
            value={statistics.currentSeason}
            subtitle="Active season"
            icon={<CalendarOutlined />}
          />
        </div>

        {/* Filters and Search */}
        <Card style={{ marginBottom: '24px' }}>
          <Space size="middle" wrap>
            <Input
              placeholder="Search leagues..."
              prefix={<SearchOutlined />}
              value={params.search}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: '300px' }}
              allowClear
            />

            <Select
              placeholder="Filter by country"
              value={params.country}
              onChange={(value) => handleFilterChange('country', value)}
              style={{ width: '150px' }}
              allowClear
            >
              <Select.Option value="England">England</Select.Option>
              <Select.Option value="Spain">Spain</Select.Option>
              <Select.Option value="Germany">Germany</Select.Option>
              <Select.Option value="Italy">Italy</Select.Option>
              <Select.Option value="France">France</Select.Option>
            </Select>

            <Select
              placeholder="Filter by season"
              value={params.season}
              onChange={(value) => handleFilterChange('season', value)}
              style={{ width: '120px' }}
              allowClear
            >
              <Select.Option value={2024}>2024</Select.Option>
              <Select.Option value={2023}>2023</Select.Option>
              <Select.Option value={2022}>2022</Select.Option>
            </Select>

            <Select
              placeholder="Filter by status"
              value={params.isActive}
              onChange={(value) => handleFilterChange('isActive', value)}
              style={{ width: '120px' }}
              allowClear
            >
              <Select.Option value={true}>Active</Select.Option>
              <Select.Option value={false}>Inactive</Select.Option>
            </Select>
          </Space>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert
            message="Error Loading Leagues"
            description="Failed to load leagues data. Please try again."
            type="error"
            showIcon
            style={{ marginBottom: '24px' }}
          />
        )}

        {/* Leagues Table */}
        <DataTable
          columns={columns}
          dataSource={leaguesData?.data || []}
          loading={isLoading}
          pagination={{
            current: params.page,
            pageSize: params.limit,
            total: leaguesData?.meta?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} leagues`,
          }}
          onChange={handleTableChange}
          rowKey="id"
          scroll={{ x: 800 }}
        />
      </Container>
    </AppLayout>
  );
}
