/**
 * Working Football Fixtures Page
 * Simplified version with minimal dependencies
 */

'use client';

import React, { useState } from 'react';
import {
  AppLayout,
  PageHeader,
  Container,
  Card,
  Button,
  Space,
  Typography,
  Table,
  Tag,
  Input,
  Select,
} from '@/components';
import {
  CalendarOutlined,
  SearchOutlined,
  VideoCameraOutlined,
  SyncOutlined,
  EyeOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const { Text } = Typography;

// Mock data for testing
const mockFixtures = [
  {
    id: 1,
    date: '2024-01-15T15:00:00Z',
    homeTeam: { name: 'Manchester United', logo: '/images/teams/mu.png' },
    awayTeam: { name: 'Liverpool', logo: '/images/teams/liverpool.png' },
    league: { name: 'Premier League', logo: '/images/leagues/pl.png' },
    status: 'NS',
    homeScore: null,
    awayScore: null,
    venue: 'Old Trafford',
  },
  {
    id: 2,
    date: '2024-01-15T17:30:00Z',
    homeTeam: { name: 'Arsenal', logo: '/images/teams/arsenal.png' },
    awayTeam: { name: 'Chelsea', logo: '/images/teams/chelsea.png' },
    league: { name: 'Premier League', logo: '/images/leagues/pl.png' },
    status: 'LIVE',
    homeScore: 2,
    awayScore: 1,
    venue: 'Emirates Stadium',
  },
  {
    id: 3,
    date: '2024-01-14T20:00:00Z',
    homeTeam: { name: 'Barcelona', logo: '/images/teams/barca.png' },
    awayTeam: { name: 'Real Madrid', logo: '/images/teams/real.png' },
    league: { name: 'La Liga', logo: '/images/leagues/laliga.png' },
    status: 'FT',
    homeScore: 3,
    awayScore: 2,
    venue: 'Camp Nou',
  },
];

export default function FixturesPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | undefined>();

  // Filter fixtures based on search and status
  const filteredFixtures = mockFixtures.filter(fixture => {
    const matchesSearch = !searchTerm ||
      fixture.homeTeam.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fixture.awayTeam.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fixture.league.name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !statusFilter || fixture.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Get status display
  const getStatusDisplay = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      'NS': { color: 'default', text: 'Not Started' },
      'LIVE': { color: 'red', text: 'Live' },
      'FT': { color: 'green', text: 'Full Time' },
      'HT': { color: 'blue', text: 'Half Time' },
    };

    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // Table columns
  const columns = [
    {
      title: 'Date & Time',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '12px' }}>
            {new Date(date).toLocaleDateString()}
          </div>
          <div style={{ fontSize: '11px', color: '#666' }}>
            {new Date(date).toLocaleTimeString()}
          </div>
        </div>
      ),
    },
    {
      title: 'Match',
      key: 'match',
      render: (_: any, fixture: any) => (
        <div style={{ minWidth: '250px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
            <span style={{ fontWeight: 'bold' }}>
              {fixture.homeTeam.name}
            </span>
            {fixture.homeScore !== null && (
              <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
                {fixture.homeScore}
              </span>
            )}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span style={{ fontWeight: 'bold' }}>
              {fixture.awayTeam.name}
            </span>
            {fixture.awayScore !== null && (
              <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
                {fixture.awayScore}
              </span>
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'League',
      dataIndex: 'league',
      key: 'league',
      render: (league: any) => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '12px' }}>
            {league.name}
          </div>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusDisplay(status),
    },
    {
      title: 'Venue',
      dataIndex: 'venue',
      key: 'venue',
      render: (venue: string) => (
        <Text style={{ fontSize: '12px' }}>{venue}</Text>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_: any, fixture: any) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => router.push(`/football/fixtures/${fixture.id}`)}
          />
          <Button
            type="text"
            icon={<TeamOutlined />}
            size="small"
          />
        </Space>
      ),
    },
  ];

  return (
    <AppLayout>
      <PageHeader
        title="Football Fixtures (Working)"
        subtitle="Manage football fixtures and match schedules"
        breadcrumbs={[
          { title: 'Home', href: '/' },
          { title: 'Football', href: '/football' },
          { title: 'Fixtures Working' },
        ]}
        actions={[
          <Button
            key="live"
            icon={<VideoCameraOutlined />}
            onClick={() => router.push('/football/fixtures/live')}
            type="primary"
            danger
          >
            Live Fixtures
          </Button>,
          <Button
            key="sync"
            icon={<SyncOutlined />}
            onClick={() => router.push('/football/sync')}
          >
            Sync Data
          </Button>,
        ]}
      />

      <Container>
        {/* Statistics Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '24px'
        }}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <CalendarOutlined style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }} />
              <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{mockFixtures.length}</div>
              <div style={{ fontSize: '14px', color: '#666' }}>Total Fixtures</div>
            </div>
          </Card>

          <Card>
            <div style={{ textAlign: 'center' }}>
              <VideoCameraOutlined style={{ fontSize: '24px', color: '#ff4d4f', marginBottom: '8px' }} />
              <div style={{ fontSize: '24px', fontWeight: 'bold' }}>
                {mockFixtures.filter(f => f.status === 'LIVE').length}
              </div>
              <div style={{ fontSize: '14px', color: '#666' }}>Live Now</div>
            </div>
          </Card>
        </div>

        {/* Filters */}
        <Card style={{ marginBottom: '24px' }}>
          <Space size="middle" wrap>
            <Input
              placeholder="Search fixtures..."
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: '250px' }}
              allowClear
            />

            <Select
              placeholder="Filter by status"
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: '140px' }}
              allowClear
            >
              <Select.Option value="NS">Not Started</Select.Option>
              <Select.Option value="LIVE">Live</Select.Option>
              <Select.Option value="FT">Finished</Select.Option>
            </Select>

            <Button
              onClick={() => {
                setSearchTerm('');
                setStatusFilter(undefined);
              }}
            >
              Clear Filters
            </Button>
          </Space>
        </Card>

        {/* Fixtures Table */}
        <Card>
          <Table
            columns={columns}
            dataSource={filteredFixtures}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} fixtures`,
            }}
            scroll={{ x: 800 }}
          />
        </Card>
      </Container>
    </AppLayout>
  );
}
