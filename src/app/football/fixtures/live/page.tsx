/**
 * Live Football Fixtures Page
 * Real-time monitoring of live football matches
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  AppLayout,
  PageHeader,
  Container,
  Card,
  Button,
  Space,
  Typography,
  Alert,
  Badge,
  Tag,
  Progress,
  Divider,
  Empty,
} from '@/components';
import {
  VideoCameraOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  SoundOutlined,
  CalendarOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { useLiveFixtures } from '@/hooks/api';
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

// Types for live fixtures
interface LiveFixture {
  id: number;
  date: string;
  status: {
    long: string;
    short: string;
    elapsed?: number;
  };
  league: {
    id: number;
    name: string;
    country: string;
    logo?: string;
    round: string;
  };
  teams: {
    home: {
      id: number;
      name: string;
      logo?: string;
      winner?: boolean;
    };
    away: {
      id: number;
      name: string;
      logo?: string;
      winner?: boolean;
    };
  };
  goals: {
    home?: number;
    away?: number;
  };
  events?: Array<{
    time: number;
    type: 'goal' | 'card' | 'substitution';
    team: 'home' | 'away';
    player: string;
    detail?: string;
  }>;
}

export default function LiveFixturesPage() {
  const router = useRouter();
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // API hooks
  const { data: liveFixtures, isLoading, error, refetch } = useLiveFixtures();

  // Auto refresh effect
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refetch();
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refetch]);

  // Mock live fixtures data
  const mockLiveFixtures: LiveFixture[] = [
    {
      id: 1,
      date: new Date().toISOString(),
      status: { long: 'Second Half', short: '2H', elapsed: 67 },
      league: {
        id: 39,
        name: 'Premier League',
        country: 'England',
        logo: '/images/premier-league.png',
        round: 'Regular Season - 15',
      },
      teams: {
        home: { id: 33, name: 'Manchester United', logo: '/images/man-utd.png' },
        away: { id: 34, name: 'Manchester City', logo: '/images/man-city.png' },
      },
      goals: { home: 2, away: 1 },
      events: [
        { time: 23, type: 'goal', team: 'home', player: 'Marcus Rashford' },
        { time: 45, type: 'goal', team: 'away', player: 'Erling Haaland' },
        { time: 56, type: 'goal', team: 'home', player: 'Bruno Fernandes' },
      ],
    },
    {
      id: 2,
      date: new Date().toISOString(),
      status: { long: 'First Half', short: '1H', elapsed: 34 },
      league: {
        id: 140,
        name: 'La Liga',
        country: 'Spain',
        logo: '/images/la-liga.png',
        round: 'Regular Season - 12',
      },
      teams: {
        home: { id: 541, name: 'Real Madrid', logo: '/images/real-madrid.png' },
        away: { id: 529, name: 'Barcelona', logo: '/images/barcelona.png' },
      },
      goals: { home: 1, away: 0 },
      events: [
        { time: 18, type: 'goal', team: 'home', player: 'Vinicius Jr.' },
      ],
    },
  ];

  const fixtures = liveFixtures?.data || mockLiveFixtures;

  // Get status display
  const getStatusDisplay = (status: LiveFixture['status']) => {
    const statusMap = {
      '1H': { color: 'orange', icon: <PlayCircleOutlined />, text: '1st Half' },
      'HT': { color: 'blue', icon: <PauseCircleOutlined />, text: 'Half Time' },
      '2H': { color: 'orange', icon: <PlayCircleOutlined />, text: '2nd Half' },
      'ET': { color: 'red', icon: <PlayCircleOutlined />, text: 'Extra Time' },
      'FT': { color: 'green', icon: <CheckCircleOutlined />, text: 'Full Time' },
    };

    const display = statusMap[status.short as keyof typeof statusMap] || {
      color: 'default',
      icon: <ClockCircleOutlined />,
      text: status.long,
    };

    return (
      <Tag icon={display.icon} color={display.color}>
        {display.text}
        {status.elapsed && ` ${status.elapsed}'`}
      </Tag>
    );
  };

  // Statistics
  const statistics = {
    liveMatches: fixtures.length,
    totalGoals: fixtures.reduce((sum, f) => sum + (f.goals.home || 0) + (f.goals.away || 0), 0),
    leagues: new Set(fixtures.map(f => f.league.id)).size,
    avgGoals: fixtures.length > 0 ?
      (fixtures.reduce((sum, f) => sum + (f.goals.home || 0) + (f.goals.away || 0), 0) / fixtures.length).toFixed(1) :
      '0.0',
  };

  return (
    <AppLayout>
      <PageHeader
        title={
          <Space>
            <VideoCameraOutlined style={{ color: '#ff4d4f' }} />
            Live Football Fixtures
          </Space>
        }
        subtitle="Real-time monitoring of live football matches"
        breadcrumbs={[
          { title: 'Home', href: '/' },
          { title: 'Football', href: '/football' },
          { title: 'Fixtures', href: '/football/fixtures' },
          { title: 'Live' },
        ]}
        actions={[
          <Button
            key="refresh"
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
          >
            Refresh
          </Button>,
          <Button
            key="auto-refresh"
            icon={<SoundOutlined />}
            type={autoRefresh ? 'primary' : 'default'}
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            Auto Refresh: {autoRefresh ? 'ON' : 'OFF'}
          </Button>,
          <Button
            key="all-fixtures"
            icon={<CalendarOutlined />}
            onClick={() => router.push('/football/fixtures')}
          >
            All Fixtures
          </Button>,
        ]}
      />

      <Container>
        {/* Auto Refresh Status */}
        {autoRefresh && (
          <Alert
            message={
              <Space>
                <VideoCameraOutlined style={{ color: '#ff4d4f' }} />
                <Text>Auto-refresh is enabled. Updates every {refreshInterval} seconds.</Text>
              </Space>
            }
            type="info"
            showIcon={false}
            style={{ marginBottom: '24px' }}
            action={
              <Button
                size="small"
                onClick={() => setAutoRefresh(false)}
              >
                Disable
              </Button>
            }
          />
        )}

        {/* Statistics Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '24px'
        }}>
          <StatCard
            title="Live Matches"
            value={statistics.liveMatches}
            subtitle="Currently playing"
            icon={<VideoCameraOutlined />}
            trend={{ value: statistics.liveMatches, isPositive: statistics.liveMatches > 0 }}
          />

          <StatCard
            title="Total Goals"
            value={statistics.totalGoals}
            subtitle="Goals scored"
            icon={<PlayCircleOutlined />}
          />

          <StatCard
            title="Active Leagues"
            value={statistics.leagues}
            subtitle="Different leagues"
            icon={<TeamOutlined />}
          />

          <StatCard
            title="Average Goals"
            value={statistics.avgGoals}
            subtitle="Per match"
            icon={<CalendarOutlined />}
          />
        </div>

        {/* Error Alert */}
        {error && (
          <Alert
            message="Error Loading Live Fixtures"
            description="Failed to load live fixtures data. Please try again."
            type="error"
            showIcon
            style={{ marginBottom: '24px' }}
          />
        )}

        {/* Live Fixtures */}
        {fixtures.length === 0 ? (
          <Card>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <Text type="secondary">No live matches at the moment</Text>
                  <br />
                  <Button
                    type="link"
                    onClick={() => router.push('/football/fixtures')}
                  >
                    View all fixtures
                  </Button>
                </div>
              }
            />
          </Card>
        ) : (
          <div style={{ display: 'grid', gap: '24px' }}>
            {fixtures.map((fixture) => (
              <Card
                key={fixture.id}
                style={{
                  border: '2px solid #ff4d4f',
                  boxShadow: '0 4px 12px rgba(255, 77, 79, 0.15)',
                }}
                bodyStyle={{ padding: '20px' }}
              >
                <div style={{ display: 'grid', gridTemplateColumns: '1fr auto 1fr', gap: '20px', alignItems: 'center' }}>
                  {/* Home Team */}
                  <div style={{ textAlign: 'right' }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: '12px', marginBottom: '8px' }}>
                      <Text style={{ fontSize: '18px', fontWeight: 'bold' }}>
                        {fixture.teams.home.name}
                      </Text>
                      {fixture.teams.home.logo && (
                        <Image
                          src={fixture.teams.home.logo}
                          alt={fixture.teams.home.name}
                          width={32}
                          height={32}
                          fallback="/images/team-placeholder.png"
                        />
                      )}
                    </div>
                  </div>

                  {/* Score and Status */}
                  <div style={{ textAlign: 'center', minWidth: '120px' }}>
                    <div style={{ fontSize: '32px', fontWeight: 'bold', marginBottom: '8px' }}>
                      <Badge count={fixture.goals.home || 0} style={{ backgroundColor: '#52c41a', marginRight: '8px' }} />
                      -
                      <Badge count={fixture.goals.away || 0} style={{ backgroundColor: '#52c41a', marginLeft: '8px' }} />
                    </div>
                    {getStatusDisplay(fixture.status)}
                  </div>

                  {/* Away Team */}
                  <div style={{ textAlign: 'left' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                      {fixture.teams.away.logo && (
                        <Image
                          src={fixture.teams.away.logo}
                          alt={fixture.teams.away.name}
                          width={32}
                          height={32}
                          fallback="/images/team-placeholder.png"
                        />
                      )}
                      <Text style={{ fontSize: '18px', fontWeight: 'bold' }}>
                        {fixture.teams.away.name}
                      </Text>
                    </div>
                  </div>
                </div>

                <Divider />

                {/* League Info */}
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    {fixture.league.logo && (
                      <Image
                        src={fixture.league.logo}
                        alt={fixture.league.name}
                        width={20}
                        height={20}
                      />
                    )}
                    <Text strong>{fixture.league.name}</Text>
                    <Text type="secondary">• {fixture.league.round}</Text>
                  </div>
                  <Text type="secondary">
                    {dayjs(fixture.date).format('HH:mm')}
                  </Text>
                </div>

                {/* Recent Events */}
                {fixture.events && fixture.events.length > 0 && (
                  <div>
                    <Text strong style={{ marginBottom: '8px', display: 'block' }}>Recent Events:</Text>
                    <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
                      {fixture.events.slice(-3).map((event, index) => (
                        <Tag
                          key={index}
                          color={event.type === 'goal' ? 'green' : event.type === 'card' ? 'red' : 'blue'}
                          style={{ margin: 0 }}
                        >
                          {event.time}' {event.player} ({event.type})
                        </Tag>
                      ))}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div style={{ marginTop: '16px', textAlign: 'center' }}>
                  <Space>
                    <Button
                      icon={<FullscreenOutlined />}
                      onClick={() => router.push(`/football/fixtures/${fixture.id}`)}
                    >
                      View Details
                    </Button>
                    <Button
                      icon={<PlayCircleOutlined />}
                      onClick={() => router.push(`/broadcast/links?fixture=${fixture.id}`)}
                    >
                      Watch Live
                    </Button>
                  </Space>
                </div>
              </Card>
            ))}
          </div>
        )}
      </Container>
    </AppLayout>
  );
}
